import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Zap, 
  Brain, 
  Calendar,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from 'lucide-react';

interface AnalyticsData {
  revenue: Array<{ month: string; actual: number; predicted: number; target: number }>;
  franchiseGrowth: Array<{ month: string; new: number; total: number; churn: number }>;
  performance: Array<{ franchise: string; revenue: number; growth: number; efficiency: number }>;
  predictions: {
    nextMonthRevenue: number;
    growthRate: number;
    riskFactors: Array<{ factor: string; impact: 'high' | 'medium' | 'low'; description: string }>;
    opportunities: Array<{ opportunity: string; potential: number; timeline: string }>;
  };
}

const AdvancedAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('12m');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<AnalyticsData | null>(null);

  useEffect(() => {
    generateAnalyticsData();
  }, [timeRange]);

  const generateAnalyticsData = async () => {
    setIsLoading(true);
    
    // Simulate API call with realistic data generation
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    const analyticsData: AnalyticsData = {
      revenue: months.map((month, index) => ({
        month,
        actual: Math.floor(Math.random() * 500000) + 800000 + (index * 50000),
        predicted: Math.floor(Math.random() * 100000) + 900000 + (index * 55000),
        target: 1000000 + (index * 50000)
      })),
      franchiseGrowth: months.map((month, index) => ({
        month,
        new: Math.floor(Math.random() * 8) + 2,
        total: 50 + (index * 3),
        churn: Math.floor(Math.random() * 2)
      })),
      performance: [
        { franchise: 'Siomai Shop', revenue: 2500000, growth: 15.2, efficiency: 92 },
        { franchise: 'Coffee Shop', revenue: 1800000, growth: 8.7, efficiency: 88 },
        { franchise: 'Burger & Fries', revenue: 3200000, growth: 22.1, efficiency: 95 },
        { franchise: 'Lemon Juice Stand', revenue: 950000, growth: 5.3, efficiency: 85 }
      ],
      predictions: {
        nextMonthRevenue: 1450000,
        growthRate: 18.5,
        riskFactors: [
          { factor: 'Seasonal Demand', impact: 'medium', description: 'Q4 typically sees 15% revenue dip' },
          { factor: 'Supply Chain', impact: 'low', description: 'Minor delays in ingredient delivery' },
          { factor: 'Competition', impact: 'high', description: 'New competitor entering market' }
        ],
        opportunities: [
          { opportunity: 'Digital Marketing Expansion', potential: 250000, timeline: '3 months' },
          { opportunity: 'New Location Openings', potential: 500000, timeline: '6 months' },
          { opportunity: 'Menu Innovation', potential: 180000, timeline: '2 months' }
        ]
      }
    };
    
    setData(analyticsData);
    setIsLoading(false);
  };

  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  const MetricCard = ({ title, value, change, icon: Icon, trend }: any) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            <div className="flex items-center mt-1">
              {trend === 'up' ? (
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                {change}
              </span>
            </div>
          </div>
          <div className="p-3 bg-blue-50 rounded-full">
            <Icon className="w-6 h-6 text-blue-600" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const PredictiveInsight = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <Card className="border-l-4 border-l-purple-500">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-purple-700">
          <Brain className="w-5 h-5" />
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Generating advanced analytics...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Advanced Analytics</h2>
          <p className="text-gray-600">AI-powered insights and predictive analytics</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3m">3 Months</SelectItem>
              <SelectItem value="6m">6 Months</SelectItem>
              <SelectItem value="12m">12 Months</SelectItem>
              <SelectItem value="24m">24 Months</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={generateAnalyticsData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          
          <Button>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Predicted Revenue"
          value={`₱${data?.predictions.nextMonthRevenue.toLocaleString()}`}
          change={`+${data?.predictions.growthRate}%`}
          icon={DollarSign}
          trend="up"
        />
        <MetricCard
          title="Growth Rate"
          value={`${data?.predictions.growthRate}%`}
          change="+2.3%"
          icon={TrendingUp}
          trend="up"
        />
        <MetricCard
          title="Active Franchises"
          value={data?.franchiseGrowth[data.franchiseGrowth.length - 1]?.total || 0}
          change="+5.2%"
          icon={Users}
          trend="up"
        />
        <MetricCard
          title="Efficiency Score"
          value="91.2%"
          change="+1.8%"
          icon={Target}
          trend="up"
        />
      </div>

      {/* Main Analytics Tabs */}
      <Tabs value={selectedMetric} onValueChange={setSelectedMetric}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
          <TabsTrigger value="growth">Growth Metrics</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="predictions">AI Predictions</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Trends & Predictions</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={data?.revenue}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value: number) => [`₱${value.toLocaleString()}`, '']} />
                  <Legend />
                  <Area 
                    type="monotone" 
                    dataKey="target" 
                    fill="#e5e7eb" 
                    stroke="#9ca3af" 
                    name="Target"
                    fillOpacity={0.3}
                  />
                  <Bar dataKey="actual" fill="#3b82f6" name="Actual Revenue" />
                  <Line 
                    type="monotone" 
                    dataKey="predicted" 
                    stroke="#10b981" 
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    name="AI Prediction"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="growth" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Franchise Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={data?.franchiseGrowth}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="total" stackId="1" stroke="#3b82f6" fill="#3b82f6" name="Total" />
                    <Area type="monotone" dataKey="new" stackId="2" stroke="#10b981" fill="#10b981" name="New" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Brand Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data?.performance}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="revenue"
                      nameKey="franchise"
                    >
                      {data?.performance.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [`₱${value.toLocaleString()}`, 'Revenue']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Franchise Performance Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.performance.map((franchise, index) => (
                  <div key={franchise.franchise} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold">{franchise.franchise}</h4>
                      <Badge variant={franchise.growth > 15 ? 'default' : franchise.growth > 8 ? 'secondary' : 'outline'}>
                        {franchise.growth > 15 ? 'High Growth' : franchise.growth > 8 ? 'Moderate' : 'Stable'}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Revenue</p>
                        <p className="font-semibold">₱{franchise.revenue.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Growth</p>
                        <p className="font-semibold text-green-600">+{franchise.growth}%</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Efficiency</p>
                        <div className="flex items-center space-x-2">
                          <Progress value={franchise.efficiency} className="flex-1" />
                          <span className="font-semibold">{franchise.efficiency}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <PredictiveInsight title="Risk Analysis">
              <div className="space-y-3">
                {data?.predictions.riskFactors.map((risk, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <AlertTriangle className={`w-5 h-5 mt-0.5 ${
                      risk.impact === 'high' ? 'text-red-500' : 
                      risk.impact === 'medium' ? 'text-yellow-500' : 'text-green-500'
                    }`} />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium">{risk.factor}</h5>
                        <Badge variant={risk.impact === 'high' ? 'destructive' : risk.impact === 'medium' ? 'secondary' : 'outline'}>
                          {risk.impact}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{risk.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </PredictiveInsight>

            <PredictiveInsight title="Growth Opportunities">
              <div className="space-y-3">
                {data?.predictions.opportunities.map((opportunity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                    <Zap className="w-5 h-5 mt-0.5 text-green-500" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium">{opportunity.opportunity}</h5>
                        <Badge className="bg-green-100 text-green-800">
                          ₱{opportunity.potential.toLocaleString()}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2 mt-1">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{opportunity.timeline}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </PredictiveInsight>
          </div>

          <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-purple-700">
                <Brain className="w-6 h-6" />
                <span>AI Recommendations</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-white rounded-lg">
                  <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <h4 className="font-semibold mb-1">Optimize High Performers</h4>
                  <p className="text-sm text-gray-600">Focus resources on Burger & Fries locations</p>
                </div>
                <div className="text-center p-4 bg-white rounded-lg">
                  <Target className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                  <h4 className="font-semibold mb-1">Expand Digital Presence</h4>
                  <p className="text-sm text-gray-600">25% revenue increase potential identified</p>
                </div>
                <div className="text-center p-4 bg-white rounded-lg">
                  <TrendingUp className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                  <h4 className="font-semibold mb-1">Seasonal Strategy</h4>
                  <p className="text-sm text-gray-600">Prepare for Q4 demand patterns</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedAnalytics;
