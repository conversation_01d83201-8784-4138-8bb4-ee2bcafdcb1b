// Offline storage and sync utilities for FranchiseHub
import React from 'react';
import { supabase } from '@/lib/supabase';

export interface OfflineAction {
  id: string;
  type: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: number;
  retryCount: number;
}

export interface CachedData {
  data: any;
  timestamp: number;
  version: string;
  expiresAt: number;
}

export class OfflineStorage {
  private static readonly PREFIX = 'franchisehub_';
  private static readonly VERSION = '1.0.0';

  // Cache management
  static setCache(key: string, data: any, ttl = 24 * 60 * 60 * 1000) { // 24 hours default
    try {
      const cached: CachedData = {
        data,
        timestamp: Date.now(),
        version: this.VERSION,
        expiresAt: Date.now() + ttl
      };
      localStorage.setItem(`${this.PREFIX}cache_${key}`, JSON.stringify(cached));
    } catch (error) {
      console.error('Failed to cache data:', error);
    }
  }

  static getCache(key: string): any | null {
    try {
      const stored = localStorage.getItem(`${this.PREFIX}cache_${key}`);
      if (!stored) return null;

      const cached: CachedData = JSON.parse(stored);

      // Check if expired
      if (Date.now() > cached.expiresAt) {
        this.removeCache(key);
        return null;
      }

      // Check version compatibility
      if (cached.version !== this.VERSION) {
        this.removeCache(key);
        return null;
      }

      return cached.data;
    } catch (error) {
      console.error('Failed to retrieve cached data:', error);
      return null;
    }
  }

  static removeCache(key: string) {
    localStorage.removeItem(`${this.PREFIX}cache_${key}`);
  }

  static clearAllCache() {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(`${this.PREFIX}cache_`)) {
        localStorage.removeItem(key);
      }
    });
  }

  // Pending actions management
  static addPendingAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>) {
    try {
      const pending = this.getPendingActions();
      const newAction: OfflineAction = {
        ...action,
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        retryCount: 0
      };

      pending.push(newAction);
      localStorage.setItem(`${this.PREFIX}pending`, JSON.stringify(pending));

      console.log('Added pending action:', newAction);
      return newAction.id;
    } catch (error) {
      console.error('Failed to store pending action:', error);
      return null;
    }
  }

  static getPendingActions(): OfflineAction[] {
    try {
      const stored = localStorage.getItem(`${this.PREFIX}pending`);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to retrieve pending actions:', error);
      return [];
    }
  }

  static removePendingAction(id: string) {
    try {
      const pending = this.getPendingActions();
      const filtered = pending.filter(action => action.id !== id);
      localStorage.setItem(`${this.PREFIX}pending`, JSON.stringify(filtered));
      console.log('Removed pending action:', id);
    } catch (error) {
      console.error('Failed to remove pending action:', error);
    }
  }

  static updatePendingAction(id: string, updates: Partial<OfflineAction>) {
    try {
      const pending = this.getPendingActions();
      const index = pending.findIndex(action => action.id === id);

      if (index !== -1) {
        pending[index] = { ...pending[index], ...updates };
        localStorage.setItem(`${this.PREFIX}pending`, JSON.stringify(pending));
      }
    } catch (error) {
      console.error('Failed to update pending action:', error);
    }
  }

  // User preferences
  static setUserPreference(key: string, value: any) {
    try {
      const prefs = this.getUserPreferences();
      prefs[key] = value;
      localStorage.setItem(`${this.PREFIX}prefs`, JSON.stringify(prefs));
    } catch (error) {
      console.error('Failed to save user preference:', error);
    }
  }

  static getUserPreference(key: string, defaultValue: any = null) {
    try {
      const prefs = this.getUserPreferences();
      return prefs[key] !== undefined ? prefs[key] : defaultValue;
    } catch (error) {
      console.error('Failed to get user preference:', error);
      return defaultValue;
    }
  }

  static getUserPreferences(): Record<string, any> {
    try {
      const stored = localStorage.getItem(`${this.PREFIX}prefs`);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return {};
    }
  }
}

export class OfflineSync {
  private static isOnline = navigator.onLine;
  private static syncInProgress = false;
  private static maxRetries = 3;

  static initialize() {
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Initial sync if online
    if (this.isOnline) {
      this.syncPendingActions();
    }
  }

  private static handleOnline() {
    console.log('🌐 Back online - syncing pending actions');
    this.isOnline = true;
    this.syncPendingActions();
  }

  private static handleOffline() {
    console.log('📱 Gone offline - actions will be queued');
    this.isOnline = false;
  }

  static async syncPendingActions() {
    if (this.syncInProgress || !this.isOnline) return;

    this.syncInProgress = true;
    const pending = OfflineStorage.getPendingActions();

    console.log(`🔄 Syncing ${pending.length} pending actions`);

    for (const action of pending) {
      try {
        await this.executeAction(action);
        OfflineStorage.removePendingAction(action.id);
        console.log('✅ Synced action:', action.id);
      } catch (error) {
        console.error('❌ Failed to sync action:', action.id, error);

        // Increment retry count
        const newRetryCount = action.retryCount + 1;

        if (newRetryCount >= this.maxRetries) {
          // Remove action after max retries
          OfflineStorage.removePendingAction(action.id);
          console.log('🗑️ Removed action after max retries:', action.id);
        } else {
          // Update retry count
          OfflineStorage.updatePendingAction(action.id, { retryCount: newRetryCount });
        }
      }
    }

    this.syncInProgress = false;
    console.log('🎉 Sync completed');
  }

  private static async executeAction(action: OfflineAction) {
    const { type, table, data } = action;

    switch (type) {
      case 'create':
        const { error: createError } = await supabase
          .from(table)
          .insert(data);
        if (createError) throw createError;
        break;

      case 'update':
        const { id, ...updateData } = data;
        const { error: updateError } = await supabase
          .from(table)
          .update(updateData)
          .eq('id', id);
        if (updateError) throw updateError;
        break;

      case 'delete':
        const { error: deleteError } = await supabase
          .from(table)
          .delete()
          .eq('id', data.id);
        if (deleteError) throw deleteError;
        break;

      default:
        throw new Error(`Unknown action type: ${type}`);
    }
  }

  static queueAction(type: OfflineAction['type'], table: string, data: any) {
    const actionId = OfflineStorage.addPendingAction({ type, table, data });

    // Try to sync immediately if online
    if (this.isOnline) {
      setTimeout(() => this.syncPendingActions(), 100);
    }

    return actionId;
  }

  static getStatus() {
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      pendingCount: OfflineStorage.getPendingActions().length
    };
  }
}

// Network status hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
};

// Offline-aware data hook
export const useOfflineData = (key: string, fetcher: () => Promise<any>, ttl?: number) => {
  const [data, setData] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const isOnline = useNetworkStatus();

  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);

      // Try to get cached data first
      const cached = OfflineStorage.getCache(key);
      if (cached) {
        setData(cached);
        setLoading(false);

        // If offline, use cached data
        if (!isOnline) return;
      }

      // If online, try to fetch fresh data
      if (isOnline) {
        try {
          const freshData = await fetcher();
          setData(freshData);
          setError(null);

          // Cache the fresh data
          OfflineStorage.setCache(key, freshData, ttl);
        } catch (err) {
          setError(err);

          // If we have cached data, use it despite the error
          if (!cached) {
            setData(null);
          }
        }
      }

      setLoading(false);
    };

    loadData();
  }, [key, isOnline, ttl]);

  return { data, loading, error, isOnline };
};

// Initialize offline sync when module loads
if (typeof window !== 'undefined') {
  OfflineSync.initialize();
}

export default { OfflineStorage, OfflineSync };
