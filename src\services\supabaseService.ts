import { supabase } from '@/lib/supabase';
import { Database } from '@/types/database';

type Tables = Database['public']['Tables'];
type FranchiseApplication = Tables['franchise_applications']['Row'];
type Franchisee = Tables['franchisees']['Row'];
type SalesReport = Tables['sales_reports']['Row'];
type SupportTicket = Tables['support_tickets']['Row'];
type Profile = Tables['profiles']['Row'];

export class SupabaseService {
  // Authentication Services
  static async signUp(email: string, password: string, metadata: any) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: { data: metadata }
    });

    if (data.user && !error) {
      // Create profile
      await supabase.from('profiles').insert({
        id: data.user.id,
        email: data.user.email!,
        full_name: metadata.full_name || '',
        role: metadata.role || 'franchisee'
      });
    }

    return { data, error };
  }

  static async signIn(email: string, password: string) {
    return await supabase.auth.signInWithPassword({ email, password });
  }

  static async signOut() {
    return await supabase.auth.signOut();
  }

  static async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  static async getCurrentProfile() {
    const user = await this.getCurrentUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    return error ? null : data;
  }

  // Franchise Applications
  static async createApplication(applicationData: Omit<FranchiseApplication, 'id' | 'created_at' | 'updated_at' | 'reviewed_by' | 'review_notes'>) {
    const { data, error } = await supabase
      .from('franchise_applications')
      .insert(applicationData)
      .select()
      .single();

    return { data, error };
  }

  static async getApplications(status?: string) {
    let query = supabase
      .from('franchise_applications')
      .select('*')
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;
    return { data: data || [], error };
  }

  static async updateApplication(id: string, updates: Partial<FranchiseApplication>) {
    const { data, error } = await supabase
      .from('franchise_applications')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    return { data, error };
  }

  static async getApplicationById(id: string) {
    const { data, error } = await supabase
      .from('franchise_applications')
      .select('*')
      .eq('id', id)
      .single();

    return { data, error };
  }

  // Franchisees
  static async createFranchisee(franchiseeData: Omit<Franchisee, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('franchisees')
      .insert(franchiseeData)
      .select()
      .single();

    return { data, error };
  }

  static async getFranchisees() {
    const { data, error } = await supabase
      .from('franchisees')
      .select(`
        *,
        profiles:profile_id (
          full_name,
          email
        )
      `)
      .order('created_at', { ascending: false });

    return { data: data || [], error };
  }

  static async getFranchiseeByProfileId(profileId: string) {
    const { data, error } = await supabase
      .from('franchisees')
      .select('*')
      .eq('profile_id', profileId)
      .single();

    return { data, error };
  }

  static async updateFranchisee(id: string, updates: Partial<Franchisee>) {
    const { data, error } = await supabase
      .from('franchisees')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    return { data, error };
  }

  // Sales Reports
  static async createSalesReport(reportData: Omit<SalesReport, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('sales_reports')
      .insert(reportData)
      .select()
      .single();

    return { data, error };
  }

  static async getSalesReports(franchiseeId?: string, dateRange?: { start: string; end: string }) {
    let query = supabase
      .from('sales_reports')
      .select(`
        *,
        franchisees:franchisee_id (
          franchise_name,
          brand,
          location
        )
      `)
      .order('report_date', { ascending: false });

    if (franchiseeId) {
      query = query.eq('franchisee_id', franchiseeId);
    }

    if (dateRange) {
      query = query
        .gte('report_date', dateRange.start)
        .lte('report_date', dateRange.end);
    }

    const { data, error } = await query;
    return { data: data || [], error };
  }

  static async updateSalesReport(id: string, updates: Partial<SalesReport>) {
    const { data, error } = await supabase
      .from('sales_reports')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    return { data, error };
  }

  // Support Tickets
  static async createSupportTicket(ticketData: Omit<SupportTicket, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('support_tickets')
      .insert(ticketData)
      .select()
      .single();

    return { data, error };
  }

  static async getSupportTickets(franchiseeId?: string, status?: string) {
    let query = supabase
      .from('support_tickets')
      .select(`
        *,
        franchisees:franchisee_id (
          franchise_name,
          brand,
          location
        )
      `)
      .order('created_at', { ascending: false });

    if (franchiseeId) {
      query = query.eq('franchisee_id', franchiseeId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;
    return { data: data || [], error };
  }

  static async updateSupportTicket(id: string, updates: Partial<SupportTicket>) {
    const { data, error } = await supabase
      .from('support_tickets')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    return { data, error };
  }

  // Analytics and Reporting
  static async getFranchiseAnalytics() {
    const { data, error } = await supabase
      .rpc('get_franchise_analytics');

    return { data: data || [], error };
  }

  static async getDashboardStats(franchiseeId?: string) {
    if (franchiseeId) {
      // Franchisee-specific stats
      const [franchisee, salesReports, tickets] = await Promise.all([
        this.getFranchiseeByProfileId(franchiseeId),
        this.getSalesReports(franchiseeId, {
          start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0]
        }),
        this.getSupportTickets(franchiseeId, 'open')
      ]);

      const totalSales = salesReports.data?.reduce((sum, report) => sum + Number(report.daily_sales), 0) || 0;
      const totalTransactions = salesReports.data?.reduce((sum, report) => sum + report.transactions_count, 0) || 0;

      return {
        franchisee: franchisee.data,
        totalSales,
        totalTransactions,
        openTickets: tickets.data?.length || 0,
        salesReports: salesReports.data || []
      };
    } else {
      // Franchisor overview stats
      const [franchisees, applications, allSales, allTickets] = await Promise.all([
        this.getFranchisees(),
        this.getApplications(),
        this.getSalesReports(),
        this.getSupportTickets()
      ]);

      const totalRevenue = allSales.data?.reduce((sum, report) => sum + Number(report.daily_sales), 0) || 0;
      const pendingApplications = applications.data?.filter(app => app.status === 'pending').length || 0;
      const openTickets = allTickets.data?.filter(ticket => ticket.status === 'open').length || 0;

      return {
        totalFranchisees: franchisees.data?.length || 0,
        totalRevenue,
        pendingApplications,
        openTickets,
        franchisees: franchisees.data || [],
        applications: applications.data || []
      };
    }
  }

  // File Upload
  static async uploadFile(bucket: string, path: string, file: File) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false
      });

    return { data, error };
  }

  static async getFileUrl(bucket: string, path: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  }

  // Real-time subscriptions
  static subscribeToTable(table: string, callback: (payload: any) => void, filter?: any) {
    let channel = supabase.channel(`public:${table}`);
    
    if (filter) {
      channel = channel.on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter: `${filter.column}=eq.${filter.value}`
        },
        callback
      );
    } else {
      channel = channel.on(
        'postgres_changes',
        { event: '*', schema: 'public', table },
        callback
      );
    }

    return channel.subscribe();
  }

  static unsubscribe(subscription: any) {
    return supabase.removeChannel(subscription);
  }
}

export default SupabaseService;
