-- Sample data for FranchiseHub
-- Run this after creating the schema

-- Insert sample franchise applications
INSERT INTO franchise_applications (
    applicant_name, email, phone, business_experience, investment_amount, 
    preferred_location, brand_interest, status, documents
) VALUES 
(
    '<PERSON>', 
    '<EMAIL>', 
    '+63 ************', 
    '5 years in food service industry', 
    500000.00, 
    'Makati City', 
    'Siomai Shop',
    'approved',
    '{"resume": "documents/robert-kim/resume.pdf", "business_plan": "documents/robert-kim/business-plan.pdf"}'
),
(
    '<PERSON>', 
    '<EMAIL>', 
    '+63 ************', 
    '3 years restaurant management', 
    750000.00, 
    'Quezon City', 
    'Burger & Fries',
    'approved',
    '{"resume": "documents/sarah-johnson/resume.pdf", "financial_statement": "documents/sarah-johnson/financial.pdf"}'
),
(
    '<PERSON>', 
    '<EMAIL>', 
    '+63 ************', 
    'First time entrepreneur', 
    300000.00, 
    '<PERSON><PERSON><PERSON>', 
    'Lemon Juice Stand',
    'under_review',
    '{"resume": "documents/michael-chen/resume.pdf"}'
),
(
    '<PERSON>', 
    '<EMAIL>', 
    '+63 ************', 
    '2 years retail experience', 
    400000.00, 
    'BGC Taguig', 
    'Coffee Shop',
    'pending',
    '{"resume": "documents/maria-santos/resume.pdf", "references": "documents/maria-santos/references.pdf"}'
);

-- Note: In a real setup, you would create users through Supabase Auth first
-- Then insert corresponding profiles. For demo purposes, we'll create sample UUIDs

-- Insert sample profiles (these would normally be created via Supabase Auth)
INSERT INTO profiles (id, email, full_name, role) VALUES 
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Admin User', 'franchisor'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Robert Kim', 'franchisee'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sarah Johnson', 'franchisee'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Michael Chen', 'franchisee');

-- Insert sample franchisees
INSERT INTO franchisees (
    profile_id, franchise_name, brand, location, opening_date, 
    monthly_sales_target, current_month_sales, total_sales, inventory_level
) VALUES 
(
    '550e8400-e29b-41d4-a716-************',
    'Siomai King - Makati',
    'Siomai Shop',
    'Ayala Avenue, Makati City',
    '2024-01-15',
    150000.00,
    89500.00,
    1250000.00,
    25
),
(
    '550e8400-e29b-41d4-a716-************',
    'Burger Palace - QC',
    'Burger & Fries',
    'Commonwealth Avenue, Quezon City',
    '2024-02-01',
    200000.00,
    145000.00,
    980000.00,
    85
),
(
    '550e8400-e29b-41d4-a716-************',
    'Fresh Lemon - Ortigas',
    'Lemon Juice Stand',
    'Ortigas Center, Pasig City',
    '2024-03-10',
    80000.00,
    45000.00,
    180000.00,
    60
);

-- Insert sample sales reports
INSERT INTO sales_reports (
    franchisee_id, report_date, daily_sales, transactions_count, top_products
) 
SELECT 
    f.id,
    CURRENT_DATE - INTERVAL '1 day' * generate_series(0, 29),
    (RANDOM() * 8000 + 2000)::DECIMAL(10,2),
    (RANDOM() * 50 + 20)::INTEGER,
    CASE f.brand
        WHEN 'Siomai Shop' THEN '{"Pork Siomai": 45, "Chicken Siomai": 32, "Beef Siomai": 28}'
        WHEN 'Burger & Fries' THEN '{"Classic Burger": 38, "Cheese Fries": 42, "Chicken Burger": 25}'
        WHEN 'Lemon Juice Stand' THEN '{"Fresh Lemonade": 55, "Iced Lemon Tea": 35, "Lemon Shake": 28}'
    END::JSONB
FROM franchisees f;

-- Insert sample inventory orders
INSERT INTO inventory_orders (
    franchisee_id, order_date, items, total_amount, status, delivery_date
)
SELECT 
    f.id,
    CURRENT_DATE - INTERVAL '1 week',
    CASE f.brand
        WHEN 'Siomai Shop' THEN '[
            {"item": "Pork Siomai Mix", "quantity": 50, "unit_price": 120.00},
            {"item": "Siomai Wrapper", "quantity": 100, "unit_price": 25.00},
            {"item": "Sauce Packets", "quantity": 200, "unit_price": 5.00}
        ]'
        WHEN 'Burger & Fries' THEN '[
            {"item": "Burger Patties", "quantity": 100, "unit_price": 45.00},
            {"item": "Burger Buns", "quantity": 100, "unit_price": 8.00},
            {"item": "French Fries", "quantity": 20, "unit_price": 85.00}
        ]'
        WHEN 'Lemon Juice Stand' THEN '[
            {"item": "Fresh Lemons", "quantity": 50, "unit_price": 15.00},
            {"item": "Sugar", "quantity": 10, "unit_price": 55.00},
            {"item": "Cups", "quantity": 500, "unit_price": 2.50}
        ]'
    END::JSONB,
    CASE f.brand
        WHEN 'Siomai Shop' THEN 9500.00
        WHEN 'Burger & Fries' THEN 7200.00
        WHEN 'Lemon Juice Stand' THEN 2050.00
    END,
    'delivered',
    CURRENT_DATE - INTERVAL '3 days'
FROM franchisees f;

-- Insert sample support tickets
INSERT INTO support_tickets (
    franchisee_id, title, description, category, priority, status
)
SELECT 
    f.id,
    'Low inventory alert for ' || f.franchise_name,
    'Running low on key ingredients. Need urgent restocking.',
    'operational',
    'high',
    'open'
FROM franchisees f
WHERE f.inventory_level < 50

UNION ALL

SELECT 
    f.id,
    'POS system issue',
    'Point of sale system is running slowly during peak hours.',
    'technical',
    'medium',
    'in_progress'
FROM franchisees f
LIMIT 1

UNION ALL

SELECT 
    f.id,
    'Marketing material request',
    'Need updated promotional materials for new product launch.',
    'marketing',
    'low',
    'resolved'
FROM franchisees f
LIMIT 1;

-- Create views for analytics
CREATE OR REPLACE VIEW franchise_analytics AS
SELECT 
    f.brand,
    COUNT(*) as total_franchises,
    SUM(f.current_month_sales) as total_monthly_sales,
    AVG(f.current_month_sales) as avg_monthly_sales,
    SUM(f.total_sales) as total_lifetime_sales,
    AVG(f.inventory_level) as avg_inventory_level
FROM franchisees f
WHERE f.status = 'active'
GROUP BY f.brand;

CREATE OR REPLACE VIEW daily_sales_summary AS
SELECT 
    sr.report_date,
    f.brand,
    SUM(sr.daily_sales) as total_sales,
    SUM(sr.transactions_count) as total_transactions,
    COUNT(DISTINCT f.id) as active_franchises
FROM sales_reports sr
JOIN franchisees f ON sr.franchisee_id = f.id
GROUP BY sr.report_date, f.brand
ORDER BY sr.report_date DESC;

-- Create functions for common queries
CREATE OR REPLACE FUNCTION get_franchise_performance(franchise_id UUID)
RETURNS TABLE (
    current_month_sales DECIMAL,
    target_achievement DECIMAL,
    total_transactions INTEGER,
    avg_daily_sales DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.current_month_sales,
        ROUND((f.current_month_sales / f.monthly_sales_target) * 100, 2) as target_achievement,
        COALESCE(SUM(sr.transactions_count)::INTEGER, 0) as total_transactions,
        COALESCE(AVG(sr.daily_sales), 0) as avg_daily_sales
    FROM franchisees f
    LEFT JOIN sales_reports sr ON f.id = sr.franchisee_id 
        AND sr.report_date >= DATE_TRUNC('month', CURRENT_DATE)
    WHERE f.id = franchise_id
    GROUP BY f.id, f.current_month_sales, f.monthly_sales_target;
END;
$$ LANGUAGE plpgsql;
