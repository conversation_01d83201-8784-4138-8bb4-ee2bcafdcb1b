import React, { useState, useEffect, useRef } from 'react';
import { Search, FileText, Users, BarChart3, Package, DollarSign, X } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: 'pages' | 'franchisees' | 'analytics' | 'inventory' | 'revenue';
  url: string;
  icon: React.ReactNode;
}

const searchData: SearchResult[] = [
  // Pages
  { id: '1', title: 'Franchisor Dashboard', description: 'Main dashboard with analytics and KPIs', category: 'pages', url: '/franchisor-dashboard', icon: <BarChart3 className="w-4 h-4" /> },
  { id: '2', title: 'Franchisee Dashboard', description: 'Franchisee management interface', category: 'pages', url: '/franchisee-dashboard', icon: <Users className="w-4 h-4" /> },
  { id: '3', title: 'Apply for Franchise', description: 'Start your franchise application', category: 'pages', url: '/apply', icon: <FileText className="w-4 h-4" /> },
  { id: '4', title: 'Blog', description: 'Latest franchise news and insights', category: 'pages', url: '/blog', icon: <FileText className="w-4 h-4" /> },
  { id: '5', title: 'Contact Us', description: 'Get in touch with our team', category: 'pages', url: '/contact', icon: <FileText className="w-4 h-4" /> },
  
  // Franchisees
  { id: '6', title: 'Robert Kim - Siomai Shop', description: 'Makati branch, Low stock alert', category: 'franchisees', url: '/franchisor-dashboard', icon: <Users className="w-4 h-4" /> },
  { id: '7', title: 'Sarah Johnson - Burger & Fries', description: 'Quezon City branch, High performer', category: 'franchisees', url: '/franchisor-dashboard', icon: <Users className="w-4 h-4" /> },
  { id: '8', title: 'Michael Chen - Lemon Juice Stand', description: 'Ortigas branch, New franchisee', category: 'franchisees', url: '/franchisor-dashboard', icon: <Users className="w-4 h-4" /> },
  
  // Analytics
  { id: '9', title: 'Sales Analytics', description: 'View sales trends and performance', category: 'analytics', url: '/franchisor-dashboard', icon: <BarChart3 className="w-4 h-4" /> },
  { id: '10', title: 'Revenue Reports', description: 'Monthly and quarterly revenue analysis', category: 'revenue', url: '/franchisor-dashboard', icon: <DollarSign className="w-4 h-4" /> },
  { id: '11', title: 'Franchise Performance', description: 'Individual franchise performance metrics', category: 'analytics', url: '/franchisor-dashboard', icon: <BarChart3 className="w-4 h-4" /> },
  
  // Inventory
  { id: '12', title: 'Inventory Management', description: 'Track and manage inventory across franchises', category: 'inventory', url: '/franchisor-dashboard', icon: <Package className="w-4 h-4" /> },
  { id: '13', title: 'Low Stock Alerts', description: 'Franchises with low inventory levels', category: 'inventory', url: '/franchisor-dashboard', icon: <Package className="w-4 h-4" /> },
  { id: '14', title: 'Supply Orders', description: 'Manage supply chain and orders', category: 'inventory', url: '/franchisor-dashboard', icon: <Package className="w-4 h-4" /> },
];

const categoryColors = {
  pages: 'bg-blue-100 text-blue-800',
  franchisees: 'bg-green-100 text-green-800',
  analytics: 'bg-purple-100 text-purple-800',
  inventory: 'bg-orange-100 text-orange-800',
  revenue: 'bg-yellow-100 text-yellow-800',
};

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (query.trim() === '') {
      setResults([]);
      return;
    }

    const filtered = searchData.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );

    setResults(filtered);
    setSelectedIndex(0);
  }, [query]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (results[selectedIndex]) {
        handleSelect(results[selectedIndex]);
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const handleSelect = (result: SearchResult) => {
    navigate(result.url);
    onClose();
    setQuery('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0">
        <DialogHeader className="p-4 pb-0">
          <DialogTitle className="sr-only">Global Search</DialogTitle>
        </DialogHeader>
        
        <div className="relative">
          <Search className="absolute left-4 top-4 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            placeholder="Search pages, franchisees, analytics..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="pl-10 pr-10 border-0 border-b rounded-none focus-visible:ring-0 text-lg h-14"
          />
          {query && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setQuery('')}
              className="absolute right-2 top-2 h-10 w-10 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {results.length > 0 && (
          <div className="max-h-96 overflow-y-auto p-4 pt-0">
            <div className="space-y-2">
              {results.map((result, index) => (
                <div
                  key={result.id}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                    index === selectedIndex
                      ? 'bg-accent text-accent-foreground'
                      : 'hover:bg-accent/50'
                  }`}
                  onClick={() => handleSelect(result)}
                >
                  <div className="flex-shrink-0">
                    {result.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium truncate">{result.title}</p>
                      <Badge variant="secondary" className={`text-xs ${categoryColors[result.category]}`}>
                        {result.category}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground truncate">{result.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {query && results.length === 0 && (
          <div className="p-8 text-center text-muted-foreground">
            <Search className="mx-auto h-8 w-8 mb-2" />
            <p>No results found for "{query}"</p>
            <p className="text-xs mt-1">Try searching for pages, franchisees, or features</p>
          </div>
        )}

        {!query && (
          <div className="p-8 text-center text-muted-foreground">
            <Search className="mx-auto h-8 w-8 mb-2" />
            <p>Start typing to search...</p>
            <div className="flex justify-center space-x-4 mt-4 text-xs">
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 bg-muted rounded text-xs">↑↓</kbd>
                <span>Navigate</span>
              </div>
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Enter</kbd>
                <span>Select</span>
              </div>
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Esc</kbd>
                <span>Close</span>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default GlobalSearch;
