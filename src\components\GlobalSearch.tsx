import React, { useState, useEffect, useRef } from 'react';
import { Search, FileText, Users, BarChart3, Package, DollarSign, X, Clock, TrendingUp, Zap, Brain, Filter } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNavigate } from 'react-router-dom';
import SupabaseService from '@/services/supabaseService';
import { useAuth } from '@/components/auth/AuthProvider';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: 'pages' | 'franchisees' | 'analytics' | 'inventory' | 'revenue' | 'applications' | 'reports' | 'ai-suggestions';
  url: string;
  icon: React.ReactNode;
  relevance?: number;
  lastAccessed?: Date;
  isAISuggestion?: boolean;
  metadata?: any;
}

const searchData: SearchResult[] = [
  // Pages
  { id: '1', title: 'Franchisor Dashboard', description: 'Main dashboard with analytics and KPIs', category: 'pages', url: '/franchisor-dashboard', icon: <BarChart3 className="w-4 h-4" /> },
  { id: '2', title: 'Franchisee Dashboard', description: 'Franchisee management interface', category: 'pages', url: '/franchisee-dashboard', icon: <Users className="w-4 h-4" /> },
  { id: '3', title: 'Apply for Franchise', description: 'Start your franchise application', category: 'pages', url: '/apply', icon: <FileText className="w-4 h-4" /> },
  { id: '4', title: 'Blog', description: 'Latest franchise news and insights', category: 'pages', url: '/blog', icon: <FileText className="w-4 h-4" /> },
  { id: '5', title: 'Contact Us', description: 'Get in touch with our team', category: 'pages', url: '/contact', icon: <FileText className="w-4 h-4" /> },

  // Franchisees
  { id: '6', title: 'Robert Kim - Siomai Shop', description: 'Makati branch, Low stock alert', category: 'franchisees', url: '/franchisor-dashboard', icon: <Users className="w-4 h-4" /> },
  { id: '7', title: 'Sarah Johnson - Burger & Fries', description: 'Quezon City branch, High performer', category: 'franchisees', url: '/franchisor-dashboard', icon: <Users className="w-4 h-4" /> },
  { id: '8', title: 'Michael Chen - Lemon Juice Stand', description: 'Ortigas branch, New franchisee', category: 'franchisees', url: '/franchisor-dashboard', icon: <Users className="w-4 h-4" /> },

  // Analytics
  { id: '9', title: 'Sales Analytics', description: 'View sales trends and performance', category: 'analytics', url: '/franchisor-dashboard', icon: <BarChart3 className="w-4 h-4" /> },
  { id: '10', title: 'Revenue Reports', description: 'Monthly and quarterly revenue analysis', category: 'revenue', url: '/franchisor-dashboard', icon: <DollarSign className="w-4 h-4" /> },
  { id: '11', title: 'Franchise Performance', description: 'Individual franchise performance metrics', category: 'analytics', url: '/franchisor-dashboard', icon: <BarChart3 className="w-4 h-4" /> },

  // Inventory
  { id: '12', title: 'Inventory Management', description: 'Track and manage inventory across franchises', category: 'inventory', url: '/franchisor-dashboard', icon: <Package className="w-4 h-4" /> },
  { id: '13', title: 'Low Stock Alerts', description: 'Franchises with low inventory levels', category: 'inventory', url: '/franchisor-dashboard', icon: <Package className="w-4 h-4" /> },
  { id: '14', title: 'Supply Orders', description: 'Manage supply chain and orders', category: 'inventory', url: '/franchisor-dashboard', icon: <Package className="w-4 h-4" /> },
];

const categoryColors = {
  pages: 'bg-blue-100 text-blue-800',
  franchisees: 'bg-green-100 text-green-800',
  analytics: 'bg-purple-100 text-purple-800',
  inventory: 'bg-orange-100 text-orange-800',
  revenue: 'bg-yellow-100 text-yellow-800',
};

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [aiSuggestions, setAiSuggestions] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'recent' | 'suggestions'>('all');
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const { user, profile } = useAuth();

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Load search history from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('search-history');
    if (saved) {
      setSearchHistory(JSON.parse(saved));
    }
  }, []);

  // Generate AI suggestions based on user role and recent activity
  useEffect(() => {
    if (isOpen) {
      generateAISuggestions();
    }
  }, [isOpen, profile]);

  const generateAISuggestions = async () => {
    const suggestions: SearchResult[] = [];

    if (profile?.role === 'franchisor') {
      suggestions.push(
        {
          id: 'ai-1',
          title: 'Review Pending Applications',
          description: 'You have 3 applications waiting for review',
          category: 'ai-suggestions',
          url: '/franchisor-dashboard',
          icon: <Brain className="w-4 h-4 text-purple-500" />,
          isAISuggestion: true,
          relevance: 95
        },
        {
          id: 'ai-2',
          title: 'Check Low Performing Franchises',
          description: 'Identify franchises below target performance',
          category: 'ai-suggestions',
          url: '/franchisor-dashboard',
          icon: <TrendingUp className="w-4 h-4 text-orange-500" />,
          isAISuggestion: true,
          relevance: 88
        }
      );
    } else if (profile?.role === 'franchisee') {
      suggestions.push(
        {
          id: 'ai-3',
          title: 'Submit Today\'s Sales Report',
          description: 'Don\'t forget to submit your daily sales',
          category: 'ai-suggestions',
          url: '/franchisee/sales-upload',
          icon: <Zap className="w-4 h-4 text-green-500" />,
          isAISuggestion: true,
          relevance: 92
        },
        {
          id: 'ai-4',
          title: 'View Performance Analytics',
          description: 'Check your monthly performance trends',
          category: 'ai-suggestions',
          url: '/franchisee-dashboard',
          icon: <BarChart3 className="w-4 h-4 text-blue-500" />,
          isAISuggestion: true,
          relevance: 85
        }
      );
    }

    setAiSuggestions(suggestions);
  };

  const performAdvancedSearch = async (searchQuery: string) => {
    setIsLoading(true);

    try {
      // Simulate AI-powered search with relevance scoring
      await new Promise(resolve => setTimeout(resolve, 300));

      const staticResults = searchData.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      // Add relevance scoring
      const scoredResults = staticResults.map(item => ({
        ...item,
        relevance: calculateRelevance(item, searchQuery)
      }));

      // Try to fetch real data from Supabase
      let dynamicResults: SearchResult[] = [];

      if (user) {
        try {
          const [applications, franchisees, salesReports] = await Promise.all([
            SupabaseService.getApplications(),
            SupabaseService.getFranchisees(),
            SupabaseService.getSalesReports()
          ]);

          // Add applications to search results
          applications.data?.forEach(app => {
            if (app.applicant_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                app.brand_interest.toLowerCase().includes(searchQuery.toLowerCase())) {
              dynamicResults.push({
                id: `app-${app.id}`,
                title: `Application: ${app.applicant_name}`,
                description: `${app.brand_interest} application - ${app.status}`,
                category: 'applications',
                url: '/franchisor-dashboard',
                icon: <FileText className="w-4 h-4" />,
                relevance: calculateRelevance({ title: app.applicant_name, description: app.brand_interest }, searchQuery),
                metadata: app
              });
            }
          });

          // Add franchisees to search results
          franchisees.data?.forEach(franchisee => {
            if (franchisee.franchise_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                franchisee.brand.toLowerCase().includes(searchQuery.toLowerCase())) {
              dynamicResults.push({
                id: `franchisee-${franchisee.id}`,
                title: franchisee.franchise_name,
                description: `${franchisee.brand} in ${franchisee.location}`,
                category: 'franchisees',
                url: '/franchisor-dashboard',
                icon: <Users className="w-4 h-4" />,
                relevance: calculateRelevance({ title: franchisee.franchise_name, description: franchisee.brand }, searchQuery),
                metadata: franchisee
              });
            }
          });
        } catch (error) {
          console.error('Error fetching dynamic search results:', error);
        }
      }

      // Combine and sort results by relevance
      const allResults = [...scoredResults, ...dynamicResults]
        .sort((a, b) => (b.relevance || 0) - (a.relevance || 0))
        .slice(0, 10); // Limit to top 10 results

      setResults(allResults);
      setSelectedIndex(0);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRelevance = (item: { title: string; description: string }, query: string): number => {
    const titleMatch = item.title.toLowerCase().includes(query.toLowerCase());
    const descMatch = item.description.toLowerCase().includes(query.toLowerCase());
    const exactMatch = item.title.toLowerCase() === query.toLowerCase();

    let score = 0;
    if (exactMatch) score += 100;
    if (titleMatch) score += 50;
    if (descMatch) score += 25;

    // Boost score for recent searches
    if (searchHistory.includes(query)) score += 10;

    return score;
  };

  useEffect(() => {
    if (query.trim() === '') {
      setResults([]);
      return;
    }

    const debounceTimer = setTimeout(() => {
      performAdvancedSearch(query);
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [query]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (results[selectedIndex]) {
        handleSelect(results[selectedIndex]);
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const handleSelect = (result: SearchResult) => {
    // Add to search history
    if (query && !searchHistory.includes(query)) {
      const newHistory = [query, ...searchHistory.slice(0, 9)]; // Keep last 10 searches
      setSearchHistory(newHistory);
      localStorage.setItem('search-history', JSON.stringify(newHistory));
    }

    navigate(result.url);
    onClose();
    setQuery('');
  };

  const getDisplayResults = () => {
    switch (activeTab) {
      case 'recent':
        return searchHistory.slice(0, 5).map(term => ({
          id: `history-${term}`,
          title: term,
          description: 'Recent search',
          category: 'pages' as const,
          url: '#',
          icon: <Clock className="w-4 h-4 text-gray-400" />,
          relevance: 0
        }));
      case 'suggestions':
        return aiSuggestions;
      default:
        return results;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0">
        <DialogHeader className="p-4 pb-0">
          <DialogTitle className="sr-only">Global Search</DialogTitle>
        </DialogHeader>

        <div className="relative">
          <Search className="absolute left-4 top-4 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            placeholder="Search pages, franchisees, analytics..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="pl-10 pr-10 border-0 border-b rounded-none focus-visible:ring-0 text-lg h-14"
          />
          {query && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setQuery('')}
              className="absolute right-2 top-2 h-10 w-10 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Search Tabs */}
        <div className="border-b">
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
            <TabsList className="grid w-full grid-cols-3 h-10 bg-transparent border-0">
              <TabsTrigger value="all" className="text-sm">All Results</TabsTrigger>
              <TabsTrigger value="recent" className="text-sm">Recent</TabsTrigger>
              <TabsTrigger value="suggestions" className="text-sm">
                <Brain className="w-3 h-3 mr-1" />
                AI Suggestions
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Results */}
        {getDisplayResults().length > 0 && (
          <div className="max-h-96 overflow-y-auto p-4 pt-0">
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-600">Searching...</span>
              </div>
            )}

            <div className="space-y-2">
              {getDisplayResults().map((result, index) => (
                <div
                  key={result.id}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                    index === selectedIndex
                      ? 'bg-accent text-accent-foreground'
                      : 'hover:bg-accent/50'
                  } ${result.isAISuggestion ? 'border border-purple-200 bg-purple-50' : ''}`}
                  onClick={() => handleSelect(result)}
                >
                  <div className="flex-shrink-0">
                    {result.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium truncate">{result.title}</p>
                      <div className="flex items-center space-x-1">
                        {result.isAISuggestion && (
                          <Badge className="text-xs bg-purple-100 text-purple-800">
                            AI
                          </Badge>
                        )}
                        <Badge variant="secondary" className={`text-xs ${categoryColors[result.category] || 'bg-gray-100 text-gray-800'}`}>
                          {result.category.replace('-', ' ')}
                        </Badge>
                        {result.relevance && result.relevance > 80 && (
                          <Badge className="text-xs bg-green-100 text-green-800">
                            {result.relevance}% match
                          </Badge>
                        )}
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground truncate">{result.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {query && results.length === 0 && (
          <div className="p-8 text-center text-muted-foreground">
            <Search className="mx-auto h-8 w-8 mb-2" />
            <p>No results found for "{query}"</p>
            <p className="text-xs mt-1">Try searching for pages, franchisees, or features</p>
          </div>
        )}

        {!query && (
          <div className="p-8 text-center text-muted-foreground">
            <Search className="mx-auto h-8 w-8 mb-2" />
            <p>Start typing to search...</p>
            <div className="flex justify-center space-x-4 mt-4 text-xs">
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 bg-muted rounded text-xs">↑↓</kbd>
                <span>Navigate</span>
              </div>
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Enter</kbd>
                <span>Select</span>
              </div>
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Esc</kbd>
                <span>Close</span>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default GlobalSearch;
