export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'franchisor' | 'franchisee' | 'admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'franchisor' | 'franchisee' | 'admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'franchisor' | 'franchisee' | 'admin'
          created_at?: string
          updated_at?: string
        }
      }
      franchise_applications: {
        Row: {
          id: string
          applicant_name: string
          email: string
          phone: string
          business_experience: string
          investment_amount: number
          preferred_location: string
          brand_interest: string
          status: 'pending' | 'approved' | 'rejected' | 'under_review'
          documents: Json | null
          created_at: string
          updated_at: string
          reviewed_by: string | null
          review_notes: string | null
        }
        Insert: {
          id?: string
          applicant_name: string
          email: string
          phone: string
          business_experience: string
          investment_amount: number
          preferred_location: string
          brand_interest: string
          status?: 'pending' | 'approved' | 'rejected' | 'under_review'
          documents?: Json | null
          created_at?: string
          updated_at?: string
          reviewed_by?: string | null
          review_notes?: string | null
        }
        Update: {
          id?: string
          applicant_name?: string
          email?: string
          phone?: string
          business_experience?: string
          investment_amount?: number
          preferred_location?: string
          brand_interest?: string
          status?: 'pending' | 'approved' | 'rejected' | 'under_review'
          documents?: Json | null
          created_at?: string
          updated_at?: string
          reviewed_by?: string | null
          review_notes?: string | null
        }
      }
      franchisees: {
        Row: {
          id: string
          profile_id: string
          franchise_name: string
          brand: string
          location: string
          opening_date: string
          status: 'active' | 'inactive' | 'suspended'
          monthly_sales_target: number
          current_month_sales: number
          total_sales: number
          inventory_level: number
          last_inventory_update: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          profile_id: string
          franchise_name: string
          brand: string
          location: string
          opening_date: string
          status?: 'active' | 'inactive' | 'suspended'
          monthly_sales_target: number
          current_month_sales?: number
          total_sales?: number
          inventory_level?: number
          last_inventory_update?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          profile_id?: string
          franchise_name?: string
          brand?: string
          location?: string
          opening_date?: string
          status?: 'active' | 'inactive' | 'suspended'
          monthly_sales_target?: number
          current_month_sales?: number
          total_sales?: number
          inventory_level?: number
          last_inventory_update?: string
          created_at?: string
          updated_at?: string
        }
      }
      sales_reports: {
        Row: {
          id: string
          franchisee_id: string
          report_date: string
          daily_sales: number
          transactions_count: number
          top_products: Json
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          franchisee_id: string
          report_date: string
          daily_sales: number
          transactions_count: number
          top_products: Json
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          franchisee_id?: string
          report_date?: string
          daily_sales?: number
          transactions_count?: number
          top_products?: Json
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      inventory_orders: {
        Row: {
          id: string
          franchisee_id: string
          order_date: string
          items: Json
          total_amount: number
          status: 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled'
          delivery_date: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          franchisee_id: string
          order_date: string
          items: Json
          total_amount: number
          status?: 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled'
          delivery_date?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          franchisee_id?: string
          order_date?: string
          items?: Json
          total_amount?: number
          status?: 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled'
          delivery_date?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      support_tickets: {
        Row: {
          id: string
          franchisee_id: string
          title: string
          description: string
          category: 'technical' | 'operational' | 'marketing' | 'financial' | 'other'
          priority: 'low' | 'medium' | 'high' | 'urgent'
          status: 'open' | 'in_progress' | 'resolved' | 'closed'
          assigned_to: string | null
          resolution: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          franchisee_id: string
          title: string
          description: string
          category: 'technical' | 'operational' | 'marketing' | 'financial' | 'other'
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          assigned_to?: string | null
          resolution?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          franchisee_id?: string
          title?: string
          description?: string
          category?: 'technical' | 'operational' | 'marketing' | 'financial' | 'other'
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          assigned_to?: string | null
          resolution?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
