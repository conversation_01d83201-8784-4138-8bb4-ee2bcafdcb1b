-- Quick Setup for FranchiseHub Supabase Database
-- Copy and paste this entire script into your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT DEFAULT 'franchisee' CHECK (role IN ('franchisor', 'franchisee', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create franchise applications table
CREATE TABLE IF NOT EXISTS franchise_applications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    applicant_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT NOT NULL,
    business_experience TEXT NOT NULL,
    investment_amount DECIMAL(12,2) NOT NULL,
    preferred_location TEXT NOT NULL,
    brand_interest TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'under_review')),
    documents JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_by UUID REFERENCES profiles(id),
    review_notes TEXT
);

-- Create franchisees table
CREATE TABLE IF NOT EXISTS franchisees (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    franchise_name TEXT NOT NULL,
    brand TEXT NOT NULL,
    location TEXT NOT NULL,
    opening_date DATE NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    monthly_sales_target DECIMAL(12,2) NOT NULL,
    current_month_sales DECIMAL(12,2) DEFAULT 0,
    total_sales DECIMAL(12,2) DEFAULT 0,
    inventory_level INTEGER DEFAULT 100,
    last_inventory_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sales reports table
CREATE TABLE IF NOT EXISTS sales_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    franchisee_id UUID REFERENCES franchisees(id) ON DELETE CASCADE,
    report_date DATE NOT NULL,
    daily_sales DECIMAL(10,2) NOT NULL,
    transactions_count INTEGER NOT NULL,
    top_products JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(franchisee_id, report_date)
);

-- Create support tickets table
CREATE TABLE IF NOT EXISTS support_tickets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    franchisee_id UUID REFERENCES franchisees(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('technical', 'operational', 'marketing', 'financial', 'other')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    assigned_to UUID REFERENCES profiles(id),
    resolution TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_franchise_applications_updated_at ON franchise_applications;
CREATE TRIGGER update_franchise_applications_updated_at BEFORE UPDATE ON franchise_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_franchisees_updated_at ON franchisees;
CREATE TRIGGER update_franchisees_updated_at BEFORE UPDATE ON franchisees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_sales_reports_updated_at ON sales_reports;
CREATE TRIGGER update_sales_reports_updated_at BEFORE UPDATE ON sales_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_support_tickets_updated_at ON support_tickets;
CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE franchise_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE franchisees ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies
-- Profiles policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Franchise applications policies (allow public insert for applications)
DROP POLICY IF EXISTS "Anyone can insert applications" ON franchise_applications;
CREATE POLICY "Anyone can insert applications" ON franchise_applications FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Users can view own applications" ON franchise_applications;
CREATE POLICY "Users can view own applications" ON franchise_applications FOR SELECT USING (email = auth.email());

-- Franchisees policies
DROP POLICY IF EXISTS "Franchisees can view own data" ON franchisees;
CREATE POLICY "Franchisees can view own data" ON franchisees FOR SELECT USING (profile_id = auth.uid());

-- Sales reports policies
DROP POLICY IF EXISTS "Franchisees can manage own sales reports" ON sales_reports;
CREATE POLICY "Franchisees can manage own sales reports" ON sales_reports FOR ALL USING (
    EXISTS (SELECT 1 FROM franchisees WHERE id = franchisee_id AND profile_id = auth.uid())
);

-- Support tickets policies
DROP POLICY IF EXISTS "Franchisees can manage own support tickets" ON support_tickets;
CREATE POLICY "Franchisees can manage own support tickets" ON support_tickets FOR ALL USING (
    EXISTS (SELECT 1 FROM franchisees WHERE id = franchisee_id AND profile_id = auth.uid())
);

-- Insert sample data
INSERT INTO franchise_applications (
    applicant_name, email, phone, business_experience, investment_amount, 
    preferred_location, brand_interest, status
) VALUES 
(
    'Robert Kim', 
    '<EMAIL>', 
    '+63 ************', 
    '5 years in food service industry', 
    500000.00, 
    'Makati City', 
    'Siomai Shop',
    'approved'
),
(
    'Sarah Johnson', 
    '<EMAIL>', 
    '+63 ************', 
    '3 years restaurant management', 
    750000.00, 
    'Quezon City', 
    'Burger & Fries',
    'approved'
),
(
    'Michael Chen', 
    '<EMAIL>', 
    '+63 ************', 
    'First time entrepreneur', 
    300000.00, 
    'Ortigas', 
    'Lemon Juice Stand',
    'under_review'
)
ON CONFLICT (id) DO NOTHING;

-- Create storage buckets (if they don't exist)
INSERT INTO storage.buckets (id, name, public) VALUES 
('documents', 'documents', false),
('avatars', 'avatars', true),
('marketing-assets', 'marketing-assets', true)
ON CONFLICT (id) DO NOTHING;

-- Success message
SELECT 'FranchiseHub database setup completed successfully!' as message;
