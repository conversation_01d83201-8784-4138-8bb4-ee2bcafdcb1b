import { useState, useEffect, useCallback } from 'react';
import { supabase, handleSupabaseError } from '@/lib/supabase';
import { Database } from '@/types/database';
import SupabaseService from '@/services/supabaseService';

type Tables = Database['public']['Tables'];

// Generic hook for Supabase operations
export function useSupabaseQuery<T>(
  table: keyof Tables,
  options?: {
    select?: string;
    filter?: Record<string, any>;
    orderBy?: { column: string; ascending?: boolean };
    limit?: number;
  }
) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        let query = supabase.from(table as string).select(options?.select || '*');

        // Apply filters
        if (options?.filter) {
          Object.entries(options.filter).forEach(([key, value]) => {
            query = query.eq(key, value);
          });
        }

        // Apply ordering
        if (options?.orderBy) {
          query = query.order(options.orderBy.column, {
            ascending: options.orderBy.ascending ?? true
          });
        }

        // Apply limit
        if (options?.limit) {
          query = query.limit(options.limit);
        }

        const { data: result, error: queryError } = await query;

        if (queryError) {
          throw queryError;
        }

        setData(result || []);
        setError(null);
      } catch (err) {
        setError(handleSupabaseError(err));
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [table, JSON.stringify(options)]);

  return { data, loading, error, refetch: () => fetchData() };
}

// Authentication hook
export function useAuth() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<any>(null);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchProfile(session.user.id);
      }
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setProfile(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const updateProfile = async (updates: any) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user?.id);

      if (error) throw error;
      await fetchProfile(user.id);
      return { error: null };
    } catch (error) {
      return { error: handleSupabaseError(error) };
    }
  };

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    isAuthenticated: !!user,
    isFranchisor: profile?.role === 'franchisor',
    isFranchisee: profile?.role === 'franchisee',
    isAdmin: profile?.role === 'admin'
  };
}

// Franchise applications hook
export function useFranchiseApplications() {
  const { data, loading, error, refetch } = useSupabaseQuery<
    Tables['franchise_applications']['Row']
  >('franchise_applications', {
    orderBy: { column: 'created_at', ascending: false }
  });

  const createApplication = async (applicationData: Tables['franchise_applications']['Insert']) => {
    try {
      const { data: result, error } = await supabase
        .from('franchise_applications')
        .insert(applicationData)
        .select()
        .single();

      if (error) throw error;
      refetch();
      return { data: result, error: null };
    } catch (err) {
      return { data: null, error: handleSupabaseError(err) };
    }
  };

  const updateApplication = async (id: string, updates: Tables['franchise_applications']['Update']) => {
    try {
      const { data: result, error } = await supabase
        .from('franchise_applications')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      refetch();
      return { data: result, error: null };
    } catch (err) {
      return { data: null, error: handleSupabaseError(err) };
    }
  };

  return {
    applications: data,
    loading,
    error,
    createApplication,
    updateApplication,
    refetch
  };
}

// Franchisees hook
export function useFranchisees() {
  const { data, loading, error, refetch } = useSupabaseQuery<
    Tables['franchisees']['Row']
  >('franchisees', {
    orderBy: { column: 'created_at', ascending: false }
  });

  const createFranchisee = async (franchiseeData: Tables['franchisees']['Insert']) => {
    try {
      const { data: result, error } = await supabase
        .from('franchisees')
        .insert(franchiseeData)
        .select()
        .single();

      if (error) throw error;
      refetch();
      return { data: result, error: null };
    } catch (err) {
      return { data: null, error: handleSupabaseError(err) };
    }
  };

  const updateFranchisee = async (id: string, updates: Tables['franchisees']['Update']) => {
    try {
      const { data: result, error } = await supabase
        .from('franchisees')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      refetch();
      return { data: result, error: null };
    } catch (err) {
      return { data: null, error: handleSupabaseError(err) };
    }
  };

  return {
    franchisees: data,
    loading,
    error,
    createFranchisee,
    updateFranchisee,
    refetch
  };
}

// Sales reports hook
export function useSalesReports(franchiseeId?: string) {
  const { data, loading, error, refetch } = useSupabaseQuery<
    Tables['sales_reports']['Row']
  >('sales_reports', {
    filter: franchiseeId ? { franchisee_id: franchiseeId } : undefined,
    orderBy: { column: 'report_date', ascending: false }
  });

  const createSalesReport = async (reportData: Tables['sales_reports']['Insert']) => {
    try {
      const { data: result, error } = await supabase
        .from('sales_reports')
        .insert(reportData)
        .select()
        .single();

      if (error) throw error;
      refetch();
      return { data: result, error: null };
    } catch (err) {
      return { data: null, error: handleSupabaseError(err) };
    }
  };

  return {
    salesReports: data,
    loading,
    error,
    createSalesReport,
    refetch
  };
}

// Real-time subscription hook
export function useRealtimeSubscription(
  table: string,
  callback: (payload: any) => void,
  filter?: { column: string; value: any }
) {
  useEffect(() => {
    let subscription: any;

    const setupSubscription = () => {
      let channel = supabase.channel(`public:${table}`);

      if (filter) {
        channel = channel.on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table,
            filter: `${filter.column}=eq.${filter.value}`
          },
          callback
        );
      } else {
        channel = channel.on(
          'postgres_changes',
          { event: '*', schema: 'public', table },
          callback
        );
      }

      subscription = channel.subscribe();
    };

    setupSubscription();

    return () => {
      if (subscription) {
        supabase.removeChannel(subscription);
      }
    };
  }, [table, callback, filter]);
}

// Enhanced Dashboard Hook
export function useDashboard() {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { profile } = useAuth();

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const franchiseeId = profile?.role === 'franchisee' ? profile.id : undefined;
      const data = await SupabaseService.getDashboardStats(franchiseeId);
      setStats(data);
      setError(null);
    } catch (err) {
      setError(handleSupabaseError(err));
    } finally {
      setLoading(false);
    }
  }, [profile]);

  useEffect(() => {
    if (profile) {
      fetchStats();
    }
  }, [profile, fetchStats]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!profile) return;

    const subscriptions: any[] = [];

    // Subscribe to relevant tables based on user role
    if (profile.role === 'franchisor') {
      subscriptions.push(
        SupabaseService.subscribeToTable('franchise_applications', fetchStats),
        SupabaseService.subscribeToTable('franchisees', fetchStats),
        SupabaseService.subscribeToTable('sales_reports', fetchStats)
      );
    } else if (profile.role === 'franchisee') {
      subscriptions.push(
        SupabaseService.subscribeToTable('sales_reports', fetchStats, { column: 'franchisee_id', value: profile.id }),
        SupabaseService.subscribeToTable('support_tickets', fetchStats, { column: 'franchisee_id', value: profile.id })
      );
    }

    return () => {
      subscriptions.forEach(sub => SupabaseService.unsubscribe(sub));
    };
  }, [profile, fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  };
}

// Enhanced Applications Hook
export function useApplicationsManagement() {
  const [applications, setApplications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApplications = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error: fetchError } = await SupabaseService.getApplications();
      if (fetchError) throw fetchError;
      setApplications(data);
      setError(null);
    } catch (err) {
      setError(handleSupabaseError(err));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const approveApplication = useCallback(async (id: string, reviewNotes?: string) => {
    try {
      const { error } = await SupabaseService.updateApplication(id, {
        status: 'approved',
        review_notes: reviewNotes
      });
      if (error) throw error;
      await fetchApplications();
      return { success: true };
    } catch (err) {
      return { success: false, error: handleSupabaseError(err) };
    }
  }, [fetchApplications]);

  const rejectApplication = useCallback(async (id: string, reviewNotes?: string) => {
    try {
      const { error } = await SupabaseService.updateApplication(id, {
        status: 'rejected',
        review_notes: reviewNotes
      });
      if (error) throw error;
      await fetchApplications();
      return { success: true };
    } catch (err) {
      return { success: false, error: handleSupabaseError(err) };
    }
  }, [fetchApplications]);

  return {
    applications,
    loading,
    error,
    approveApplication,
    rejectApplication,
    refetch: fetchApplications
  };
}

// File Upload Hook
export function useFileUpload() {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadFile = useCallback(async (file: File, bucket: string, path: string) => {
    try {
      setUploading(true);
      setError(null);

      const { data, error: uploadError } = await SupabaseService.uploadFile(bucket, path, file);
      if (uploadError) throw uploadError;

      const url = await SupabaseService.getFileUrl(bucket, path);
      return { success: true, url, data };
    } catch (err) {
      const errorMessage = handleSupabaseError(err);
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setUploading(false);
    }
  }, []);

  return {
    uploadFile,
    uploading,
    error
  };
}
