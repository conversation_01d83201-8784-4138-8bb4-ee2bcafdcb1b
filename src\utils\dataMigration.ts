import SupabaseService from '@/services/supabaseService';

// Mock data to migrate to Supabase
const mockApplications = [
  {
    applicant_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+63 ************',
    business_experience: '3 years in retail management',
    investment_amount: 120000,
    preferred_location: 'BGC, Taguig',
    brand_interest: 'Siomai Shop'
  },
  {
    applicant_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+63 ************',
    business_experience: '5 years in food service',
    investment_amount: 250000,
    preferred_location: 'Makati City',
    brand_interest: 'Coffee Shop'
  },
  {
    applicant_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+63 ************',
    business_experience: 'First time entrepreneur',
    investment_amount: 50000,
    preferred_location: 'Quezon City',
    brand_interest: 'Lemon Juice Stand'
  }
];

const mockSalesReports = [
  {
    report_date: '2024-01-15',
    daily_sales: 4500.00,
    transactions_count: 45,
    top_products: {
      'Pork Siomai': 30,
      'Chicken Siomai': 25,
      'Beef Siomai': 20
    },
    notes: 'Good sales day, high customer traffic'
  },
  {
    report_date: '2024-01-14',
    daily_sales: 3800.00,
    transactions_count: 38,
    top_products: {
      'Pork Siomai': 25,
      'Chicken Siomai': 20,
      'Beef Siomai': 15
    },
    notes: 'Regular weekday sales'
  },
  {
    report_date: '2024-01-13',
    daily_sales: 5200.00,
    transactions_count: 52,
    top_products: {
      'Pork Siomai': 35,
      'Chicken Siomai': 30,
      'Beef Siomai': 25
    },
    notes: 'Weekend rush, very busy'
  }
];

const mockSupportTickets = [
  {
    title: 'POS System Issue',
    description: 'The point of sale system is running slowly during peak hours. Need technical support.',
    category: 'technical' as const,
    priority: 'high' as const
  },
  {
    title: 'Inventory Shortage',
    description: 'Running low on siomai mix and sauce packets. Need urgent restocking.',
    category: 'operational' as const,
    priority: 'urgent' as const
  },
  {
    title: 'Marketing Materials Request',
    description: 'Need updated promotional materials for the new product launch.',
    category: 'marketing' as const,
    priority: 'medium' as const
  }
];

export class DataMigration {
  static async migrateApplications() {
    console.log('🔄 Migrating franchise applications...');
    const results = [];
    
    for (const app of mockApplications) {
      try {
        const { data, error } = await SupabaseService.createApplication(app);
        if (error) {
          console.error('❌ Failed to migrate application:', app.applicant_name, error);
          results.push({ success: false, item: app.applicant_name, error });
        } else {
          console.log('✅ Migrated application:', app.applicant_name);
          results.push({ success: true, item: app.applicant_name, data });
        }
      } catch (error) {
        console.error('❌ Error migrating application:', app.applicant_name, error);
        results.push({ success: false, item: app.applicant_name, error });
      }
    }
    
    return results;
  }

  static async migrateSalesReports(franchiseeId: string) {
    console.log('🔄 Migrating sales reports...');
    const results = [];
    
    for (const report of mockSalesReports) {
      try {
        const reportData = {
          ...report,
          franchisee_id: franchiseeId
        };
        
        const { data, error } = await SupabaseService.createSalesReport(reportData);
        if (error) {
          console.error('❌ Failed to migrate sales report:', report.report_date, error);
          results.push({ success: false, item: report.report_date, error });
        } else {
          console.log('✅ Migrated sales report:', report.report_date);
          results.push({ success: true, item: report.report_date, data });
        }
      } catch (error) {
        console.error('❌ Error migrating sales report:', report.report_date, error);
        results.push({ success: false, item: report.report_date, error });
      }
    }
    
    return results;
  }

  static async migrateSupportTickets(franchiseeId: string) {
    console.log('🔄 Migrating support tickets...');
    const results = [];
    
    for (const ticket of mockSupportTickets) {
      try {
        const ticketData = {
          ...ticket,
          franchisee_id: franchiseeId
        };
        
        const { data, error } = await SupabaseService.createSupportTicket(ticketData);
        if (error) {
          console.error('❌ Failed to migrate support ticket:', ticket.title, error);
          results.push({ success: false, item: ticket.title, error });
        } else {
          console.log('✅ Migrated support ticket:', ticket.title);
          results.push({ success: true, item: ticket.title, data });
        }
      } catch (error) {
        console.error('❌ Error migrating support ticket:', ticket.title, error);
        results.push({ success: false, item: ticket.title, error });
      }
    }
    
    return results;
  }

  static async createSampleFranchisee(profileId: string) {
    console.log('🔄 Creating sample franchisee...');
    
    const franchiseeData = {
      profile_id: profileId,
      franchise_name: 'Siomai King - Makati',
      brand: 'Siomai Shop',
      location: 'Ayala Avenue, Makati City',
      opening_date: '2024-01-15',
      monthly_sales_target: 150000.00,
      current_month_sales: 89500.00,
      total_sales: 1250000.00,
      inventory_level: 75
    };

    try {
      const { data, error } = await SupabaseService.createFranchisee(franchiseeData);
      if (error) {
        console.error('❌ Failed to create franchisee:', error);
        return { success: false, error };
      } else {
        console.log('✅ Created sample franchisee:', data.franchise_name);
        return { success: true, data };
      }
    } catch (error) {
      console.error('❌ Error creating franchisee:', error);
      return { success: false, error };
    }
  }

  static async runFullMigration(profileId?: string) {
    console.log('🚀 Starting full data migration...');
    
    const results = {
      applications: await this.migrateApplications(),
      franchisee: null as any,
      salesReports: [] as any[],
      supportTickets: [] as any[]
    };

    // If profileId is provided, create franchisee and related data
    if (profileId) {
      results.franchisee = await this.createSampleFranchisee(profileId);
      
      if (results.franchisee.success) {
        const franchiseeId = results.franchisee.data.id;
        results.salesReports = await this.migrateSalesReports(franchiseeId);
        results.supportTickets = await this.migrateSupportTickets(franchiseeId);
      }
    }

    // Summary
    const summary = {
      applications: {
        total: results.applications.length,
        successful: results.applications.filter(r => r.success).length,
        failed: results.applications.filter(r => !r.success).length
      },
      salesReports: {
        total: results.salesReports.length,
        successful: results.salesReports.filter(r => r.success).length,
        failed: results.salesReports.filter(r => !r.success).length
      },
      supportTickets: {
        total: results.supportTickets.length,
        successful: results.supportTickets.filter(r => r.success).length,
        failed: results.supportTickets.filter(r => !r.success).length
      }
    };

    console.log('📊 Migration Summary:', summary);
    
    return { results, summary };
  }

  static async clearAllData() {
    console.log('🗑️ This would clear all data (not implemented for safety)');
    // Intentionally not implemented to prevent accidental data loss
    // In a real scenario, you'd implement this with proper safeguards
    return { message: 'Data clearing not implemented for safety' };
  }
}

export default DataMigration;
