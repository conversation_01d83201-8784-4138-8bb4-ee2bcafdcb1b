import { supabase } from '@/lib/supabase';

export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export class EmailService {
  // Application Status Notifications
  static async sendApplicationReceived(applicantEmail: string, applicantName: string, applicationId: string) {
    const template = {
      to: applicantEmail,
      subject: '🎉 Application Received - FranchiseHub',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">FranchiseHub</h1>
            <p style="color: #6b7280; margin: 5px 0;">Your Franchise Journey Starts Here</p>
          </div>
          
          <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h2 style="margin: 0 0 10px 0;">Application Received!</h2>
            <p style="margin: 0; opacity: 0.9;">We've received your franchise application and are excited to review it.</p>
          </div>
          
          <div style="background: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="color: #1f2937; margin: 0 0 15px 0;">Application Details</h3>
            <p><strong>Applicant:</strong> ${applicantName}</p>
            <p><strong>Application ID:</strong> ${applicationId}</p>
            <p><strong>Status:</strong> <span style="color: #f59e0b;">Under Review</span></p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
          
          <div style="background: #ecfdf5; border-left: 4px solid #10b981; padding: 20px; margin-bottom: 25px;">
            <h4 style="color: #065f46; margin: 0 0 10px 0;">What's Next?</h4>
            <ul style="color: #047857; margin: 0; padding-left: 20px;">
              <li>Our team will review your application within 2-3 business days</li>
              <li>We may contact you for additional information or clarification</li>
              <li>You'll receive an email notification once a decision is made</li>
              <li>Approved applicants will be contacted for the next steps</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-bottom: 25px;">
            <a href="${window.location.origin}/contact" 
               style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Contact Support
            </a>
          </div>
          
          <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center; color: #6b7280; font-size: 14px;">
            <p>Thank you for choosing FranchiseHub!</p>
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
          </div>
        </div>
      `,
      text: `
        Application Received - FranchiseHub
        
        Dear ${applicantName},
        
        We've received your franchise application (ID: ${applicationId}) and are excited to review it.
        
        What's Next:
        - Our team will review your application within 2-3 business days
        - We may contact you for additional information
        - You'll receive an email notification once a decision is made
        
        Thank you for choosing FranchiseHub!
      `
    };

    return this.sendEmail(template);
  }

  static async sendApplicationApproved(applicantEmail: string, applicantName: string, brandInterest: string) {
    const template = {
      to: applicantEmail,
      subject: '🎉 Congratulations! Your Application Has Been Approved',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">FranchiseHub</h1>
          </div>
          
          <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h2 style="margin: 0 0 10px 0;">🎉 Congratulations!</h2>
            <p style="margin: 0; opacity: 0.9;">Your ${brandInterest} franchise application has been approved!</p>
          </div>
          
          <div style="background: #f0fdf4; border: 1px solid #bbf7d0; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="color: #166534; margin: 0 0 15px 0;">Welcome to the FranchiseHub Family!</h3>
            <p style="color: #15803d;">We're thrilled to have you join our network of successful franchise partners.</p>
          </div>
          
          <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin-bottom: 25px;">
            <h4 style="color: #92400e; margin: 0 0 10px 0;">Next Steps</h4>
            <ol style="color: #b45309; margin: 0; padding-left: 20px;">
              <li>Our franchise coordinator will contact you within 24 hours</li>
              <li>Schedule your onboarding session and training</li>
              <li>Review and sign the franchise agreement</li>
              <li>Begin your exciting franchise journey!</li>
            </ol>
          </div>
          
          <div style="text-align: center; margin-bottom: 25px;">
            <a href="${window.location.origin}/franchisee-training" 
               style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-right: 10px;">
              Start Training
            </a>
            <a href="${window.location.origin}/contact" 
               style="background: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Contact Us
            </a>
          </div>
          
          <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center; color: #6b7280; font-size: 14px;">
            <p>Welcome to FranchiseHub - Your success is our success!</p>
          </div>
        </div>
      `
    };

    return this.sendEmail(template);
  }

  static async sendApplicationRejected(applicantEmail: string, applicantName: string, reason?: string) {
    const template = {
      to: applicantEmail,
      subject: 'Application Update - FranchiseHub',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">FranchiseHub</h1>
          </div>
          
          <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="color: #991b1b; margin: 0 0 15px 0;">Application Update</h3>
            <p style="color: #7f1d1d;">Thank you for your interest in joining the FranchiseHub network. After careful review, we are unable to approve your application at this time.</p>
            ${reason ? `<p style="color: #7f1d1d;"><strong>Reason:</strong> ${reason}</p>` : ''}
          </div>
          
          <div style="background: #eff6ff; border-left: 4px solid #3b82f6; padding: 20px; margin-bottom: 25px;">
            <h4 style="color: #1e40af; margin: 0 0 10px 0;">Don't Give Up!</h4>
            <p style="color: #1d4ed8;">We encourage you to reapply in the future. Consider:</p>
            <ul style="color: #1d4ed8; margin: 0; padding-left: 20px;">
              <li>Gaining additional business experience</li>
              <li>Improving your financial position</li>
              <li>Exploring different franchise opportunities</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-bottom: 25px;">
            <a href="${window.location.origin}/apply" 
               style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Apply Again
            </a>
          </div>
        </div>
      `
    };

    return this.sendEmail(template);
  }

  // Sales Report Notifications
  static async sendSalesReportReceived(franchiseeEmail: string, franchiseeName: string, reportDate: string, amount: number) {
    const template = {
      to: franchiseeEmail,
      subject: '✅ Sales Report Received - FranchiseHub',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #10b981; color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
            <h2 style="margin: 0;">Sales Report Received</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">${franchiseeName}</p>
          </div>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
            <h3>Report Details</h3>
            <p><strong>Date:</strong> ${new Date(reportDate).toLocaleDateString()}</p>
            <p><strong>Sales Amount:</strong> ₱${amount.toLocaleString()}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Received</span></p>
          </div>
          
          <div style="text-align: center; margin-top: 20px;">
            <a href="${window.location.origin}/franchisee-dashboard" 
               style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              View Dashboard
            </a>
          </div>
        </div>
      `
    };

    return this.sendEmail(template);
  }

  // Support Ticket Notifications
  static async sendSupportTicketCreated(franchiseeEmail: string, ticketId: string, title: string) {
    const template = {
      to: franchiseeEmail,
      subject: `🎫 Support Ticket Created #${ticketId.slice(0, 8)}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #3b82f6; color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
            <h2 style="margin: 0;">Support Ticket Created</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Ticket #${ticketId.slice(0, 8)}</p>
          </div>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
            <h3>${title}</h3>
            <p>Your support request has been received and assigned to our team. We'll respond within 24 hours.</p>
          </div>
        </div>
      `
    };

    return this.sendEmail(template);
  }

  // Generic email sender (would integrate with actual email service)
  private static async sendEmail(template: EmailTemplate): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real implementation, this would integrate with:
      // - SendGrid, Mailgun, AWS SES, or similar service
      // - Supabase Edge Functions for server-side email sending
      
      console.log('📧 Email would be sent:', {
        to: template.to,
        subject: template.subject,
        preview: template.html.substring(0, 100) + '...'
      });

      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // For demo purposes, show a notification
      if (typeof window !== 'undefined') {
        const notification = document.createElement('div');
        notification.innerHTML = `
          <div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10000; max-width: 300px;">
            <div style="font-weight: 600; margin-bottom: 5px;">📧 Email Sent</div>
            <div style="font-size: 14px; opacity: 0.9;">${template.subject}</div>
            <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">To: ${template.to}</div>
          </div>
        `;
        document.body.appendChild(notification);
        
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 4000);
      }

      return { success: true };
    } catch (error) {
      console.error('Email sending failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  // Batch email sending for announcements
  static async sendBulkEmail(recipients: string[], subject: string, html: string) {
    const results = await Promise.all(
      recipients.map(email => this.sendEmail({ to: email, subject, html }))
    );
    
    return {
      sent: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      total: recipients.length
    };
  }
}

export default EmailService;
