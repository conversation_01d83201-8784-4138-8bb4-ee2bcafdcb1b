# 🗄️ Supabase Setup Guide for FranchiseHub

## 📋 Prerequisites

- Supabase account (free tier available)
- Node.js 18+ installed
- FranchiseHub project cloned locally

## 🚀 Quick Setup Steps

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Create a new organization (if needed)
4. Click "New Project"
5. Fill in project details:
   - **Name**: `FranchiseHub`
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your users (e.g., Southeast Asia)
6. Click "Create new project"

### 2. Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Project API Key** (anon/public key)
   - **Service Role Key** (for admin operations)

### 3. Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update your `.env` file with Supabase credentials:
   ```env
   VITE_SUPABASE_URL=https://your-project-ref.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   VITE_SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

### 4. Install Dependencies

```bash
npm install @supabase/supabase-js
```

### 5. Set Up Database Schema

1. In Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `database/schema.sql`
3. Paste and run the SQL script
4. This will create all tables, policies, and functions

### 6. Add Sample Data (Optional)

1. In SQL Editor, copy contents of `database/sample-data.sql`
2. Run the script to populate with demo data
3. This creates sample franchisees, applications, and reports

### 7. Configure Storage

1. Go to **Storage** in Supabase dashboard
2. The schema script already created buckets:
   - `documents` - For application documents
   - `avatars` - For user profile pictures
   - `marketing-assets` - For promotional materials

### 8. Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Check browser console for any Supabase connection errors
3. Try creating a franchise application to test database writes

## 🔐 Authentication Setup

### Enable Email Authentication

1. Go to **Authentication** → **Settings**
2. Under **Auth Providers**, ensure **Email** is enabled
3. Configure email templates if needed
4. Set up SMTP for production (optional for development)

### Configure Redirect URLs

1. In **Authentication** → **URL Configuration**
2. Add your site URLs:
   - **Site URL**: `http://localhost:8080` (development)
   - **Redirect URLs**: Add production URLs when deploying

## 📊 Database Structure

### Core Tables

- **profiles** - User profiles extending Supabase auth
- **franchise_applications** - Franchise application submissions
- **franchisees** - Active franchise locations
- **sales_reports** - Daily sales data from franchisees
- **inventory_orders** - Supply chain management
- **support_tickets** - Customer support system

### Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Role-based access** (franchisor, franchisee, admin)
- **Secure file uploads** with user-specific folders
- **Real-time subscriptions** for live updates

## 🔧 Development Tips

### Using the Supabase Client

```typescript
import { supabase } from '@/lib/supabase';

// Query data
const { data, error } = await supabase
  .from('franchisees')
  .select('*')
  .eq('status', 'active');

// Insert data
const { data, error } = await supabase
  .from('sales_reports')
  .insert({
    franchisee_id: 'uuid',
    report_date: '2024-01-15',
    daily_sales: 5000.00,
    transactions_count: 45
  });
```

### Using React Hooks

```typescript
import { useAuth, useFranchisees } from '@/hooks/useSupabase';

function Dashboard() {
  const { user, profile, isAuthenticated } = useAuth();
  const { franchisees, loading, error } = useFranchisees();

  if (!isAuthenticated) return <Login />;
  
  return (
    <div>
      <h1>Welcome, {profile?.full_name}</h1>
      {/* Dashboard content */}
    </div>
  );
}
```

### Real-time Updates

```typescript
import { useRealtimeSubscription } from '@/hooks/useSupabase';

function SalesReports() {
  useRealtimeSubscription('sales_reports', (payload) => {
    console.log('New sales report:', payload);
    // Update UI with new data
  });
}
```

## 🚀 Production Deployment

### Environment Variables

Set these in your production environment:

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

### Security Checklist

- [ ] RLS policies tested and working
- [ ] Service role key kept secure (server-side only)
- [ ] CORS settings configured for your domain
- [ ] Email templates customized
- [ ] Storage policies tested
- [ ] Backup strategy in place

## 🔍 Troubleshooting

### Common Issues

1. **Connection Error**
   - Check environment variables are correct
   - Verify project URL and API key
   - Ensure project is not paused

2. **Permission Denied**
   - Check RLS policies are set up correctly
   - Verify user authentication
   - Check user role assignments

3. **Real-time Not Working**
   - Ensure RLS policies allow SELECT
   - Check subscription setup
   - Verify table name spelling

### Getting Help

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

## 📈 Next Steps

After setting up Supabase:

1. **Implement Authentication** - Add login/signup forms
2. **Connect Real Data** - Replace mock data with Supabase queries
3. **Add Real-time Features** - Live updates for dashboards
4. **File Uploads** - Document and image upload functionality
5. **Advanced Analytics** - Custom queries and reporting

## 🎯 Benefits of Supabase Integration

- ✅ **Real-time data** across all users
- ✅ **Secure authentication** with role-based access
- ✅ **Scalable database** with PostgreSQL
- ✅ **File storage** for documents and images
- ✅ **Real-time subscriptions** for live updates
- ✅ **Built-in API** with automatic REST endpoints
- ✅ **Row-level security** for data protection

Your FranchiseHub platform will now have a production-ready backend! 🎉
