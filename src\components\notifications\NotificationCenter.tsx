import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Bell, 
  BellRing, 
  Check, 
  X, 
  Settings, 
  Mail, 
  MessageSquare, 
  AlertTriangle,
  CheckCircle,
  Info,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  Filter,
  MoreHorizontal
} from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';
import { useRealtimeSubscription } from '@/hooks/useSupabase';

interface Notification {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info' | 'application' | 'sales' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'applications' | 'sales' | 'system' | 'marketing' | 'support';
}

interface NotificationSettings {
  email: boolean;
  push: boolean;
  applications: boolean;
  sales: boolean;
  system: boolean;
  marketing: boolean;
  support: boolean;
}

const NotificationCenter: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'applications' | 'sales' | 'system'>('all');
  const [settings, setSettings] = useState<NotificationSettings>({
    email: true,
    push: true,
    applications: true,
    sales: true,
    system: true,
    marketing: false,
    support: true
  });

  const { user, profile } = useAuth();

  // Initialize with sample notifications
  useEffect(() => {
    const sampleNotifications: Notification[] = [
      {
        id: '1',
        type: 'application',
        title: 'New Franchise Application',
        message: 'Maria Santos submitted an application for Siomai Shop in BGC',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        read: false,
        actionUrl: '/franchisor-dashboard',
        actionText: 'Review Application',
        priority: 'high',
        category: 'applications'
      },
      {
        id: '2',
        type: 'sales',
        title: 'Sales Target Achieved',
        message: 'Makati branch exceeded monthly target by 15%',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: false,
        priority: 'medium',
        category: 'sales'
      },
      {
        id: '3',
        type: 'warning',
        title: 'Low Inventory Alert',
        message: 'Quezon City branch running low on siomai mix',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        read: true,
        actionUrl: '/franchisor-dashboard',
        actionText: 'Manage Inventory',
        priority: 'urgent',
        category: 'system'
      },
      {
        id: '4',
        type: 'success',
        title: 'Training Completed',
        message: 'Robert Kim completed advanced franchise training',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        read: true,
        priority: 'low',
        category: 'support'
      },
      {
        id: '5',
        type: 'info',
        title: 'System Maintenance',
        message: 'Scheduled maintenance tonight from 2-4 AM',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        read: false,
        priority: 'medium',
        category: 'system'
      }
    ];

    setNotifications(sampleNotifications);
    setUnreadCount(sampleNotifications.filter(n => !n.read).length);
  }, []);

  // Real-time notification updates
  useRealtimeSubscription('franchise_applications', (payload) => {
    if (payload.eventType === 'INSERT') {
      addNotification({
        type: 'application',
        title: 'New Franchise Application',
        message: `${payload.new.applicant_name} submitted an application for ${payload.new.brand_interest}`,
        priority: 'high',
        category: 'applications',
        actionUrl: '/franchisor-dashboard',
        actionText: 'Review Application'
      });
    }
  });

  useRealtimeSubscription('sales_reports', (payload) => {
    if (payload.eventType === 'INSERT') {
      addNotification({
        type: 'sales',
        title: 'New Sales Report',
        message: `Sales report submitted: ₱${payload.new.daily_sales.toLocaleString()}`,
        priority: 'medium',
        category: 'sales'
      });
    }
  });

  const addNotification = (notificationData: Partial<Notification>) => {
    const newNotification: Notification = {
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false,
      ...notificationData
    } as Notification;

    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(newNotification.title, {
        body: newNotification.message,
        icon: '/favicon.ico',
        tag: newNotification.id
      });
    }

    // Auto-hide after 5 seconds for non-urgent notifications
    if (newNotification.priority !== 'urgent') {
      setTimeout(() => {
        markAsRead(newNotification.id);
      }, 5000);
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
    const notification = notifications.find(n => n.id === id);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const getFilteredNotifications = () => {
    return notifications.filter(notification => {
      if (filter === 'unread') return !notification.read;
      if (filter === 'all') return true;
      return notification.category === filter;
    });
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'error': return <X className="w-5 h-5 text-red-500" />;
      case 'application': return <Users className="w-5 h-5 text-blue-500" />;
      case 'sales': return <DollarSign className="w-5 h-5 text-green-500" />;
      case 'system': return <Settings className="w-5 h-5 text-gray-500" />;
      default: return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="sm"
        className="relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        {unreadCount > 0 ? (
          <BellRing className="w-5 h-5" />
        ) : (
          <Bell className="w-5 h-5" />
        )}
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-white border rounded-lg shadow-lg z-50">
          <Card className="border-0 shadow-none">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Notifications</CardTitle>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                      <Check className="w-4 h-4 mr-1" />
                      Mark all read
                    </Button>
                  )}
                  <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              {/* Filter Tabs */}
              <Tabs value={filter} onValueChange={(value: any) => setFilter(value)}>
                <TabsList className="grid w-full grid-cols-5 h-8">
                  <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
                  <TabsTrigger value="unread" className="text-xs">Unread</TabsTrigger>
                  <TabsTrigger value="applications" className="text-xs">Apps</TabsTrigger>
                  <TabsTrigger value="sales" className="text-xs">Sales</TabsTrigger>
                  <TabsTrigger value="system" className="text-xs">System</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>

            <CardContent className="p-0">
              <ScrollArea className="h-96">
                <div className="space-y-1 p-3">
                  {getFilteredNotifications().length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>No notifications</p>
                    </div>
                  ) : (
                    getFilteredNotifications().map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-3 rounded-lg border transition-colors ${
                          notification.read 
                            ? 'bg-gray-50 border-gray-200' 
                            : 'bg-blue-50 border-blue-200'
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-0.5">
                            {getNotificationIcon(notification.type)}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="text-sm font-medium truncate">
                                {notification.title}
                              </h4>
                              <div className="flex items-center space-x-1">
                                <Badge className={`text-xs ${getPriorityColor(notification.priority)}`}>
                                  {notification.priority}
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() => deleteNotification(notification.id)}
                                >
                                  <X className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                            
                            <p className="text-sm text-gray-600 mb-2">
                              {notification.message}
                            </p>
                            
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-1 text-xs text-gray-500">
                                <Clock className="w-3 h-3" />
                                <span>{formatTimestamp(notification.timestamp)}</span>
                              </div>
                              
                              <div className="flex items-center space-x-2">
                                {notification.actionUrl && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-6 text-xs"
                                    onClick={() => {
                                      markAsRead(notification.id);
                                      window.location.href = notification.actionUrl!;
                                    }}
                                  >
                                    {notification.actionText || 'View'}
                                  </Button>
                                )}
                                
                                {!notification.read && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 text-xs"
                                    onClick={() => markAsRead(notification.id)}
                                  >
                                    Mark read
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
