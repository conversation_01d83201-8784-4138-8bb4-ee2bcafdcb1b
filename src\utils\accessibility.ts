// Accessibility utilities and helpers

/**
 * Announces text to screen readers
 */
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Manages focus for modal dialogs and overlays
 */
export class FocusManager {
  private previousFocus: HTMLElement | null = null;
  private focusableElements: HTMLElement[] = [];

  constructor(private container: HTMLElement) {
    this.updateFocusableElements();
  }

  private updateFocusableElements() {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    this.focusableElements = Array.from(
      this.container.querySelectorAll(focusableSelectors)
    ) as HTMLElement[];
  }

  trapFocus() {
    this.previousFocus = document.activeElement as HTMLElement;
    
    if (this.focusableElements.length > 0) {
      this.focusableElements[0].focus();
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        const firstElement = this.focusableElements[0];
        const lastElement = this.focusableElements[this.focusableElements.length - 1];

        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement?.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      this.restoreFocus();
    };
  }

  restoreFocus() {
    if (this.previousFocus) {
      this.previousFocus.focus();
    }
  }
}

/**
 * Generates accessible IDs for form elements
 */
export function generateAccessibleId(prefix: string = 'element'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Creates ARIA label for form fields with validation
 */
export function createAriaLabel(
  label: string,
  required: boolean = false,
  error?: string,
  description?: string
): string {
  let ariaLabel = label;
  
  if (required) {
    ariaLabel += ', required';
  }
  
  if (description) {
    ariaLabel += `, ${description}`;
  }
  
  if (error) {
    ariaLabel += `, error: ${error}`;
  }
  
  return ariaLabel;
}

/**
 * Keyboard navigation helpers
 */
export const KeyboardNavigation = {
  /**
   * Handle arrow key navigation in lists
   */
  handleArrowNavigation: (
    e: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (index: number) => void
  ) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        const nextIndex = Math.min(currentIndex + 1, items.length - 1);
        onIndexChange(nextIndex);
        items[nextIndex]?.focus();
        break;
      case 'ArrowUp':
        e.preventDefault();
        const prevIndex = Math.max(currentIndex - 1, 0);
        onIndexChange(prevIndex);
        items[prevIndex]?.focus();
        break;
      case 'Home':
        e.preventDefault();
        onIndexChange(0);
        items[0]?.focus();
        break;
      case 'End':
        e.preventDefault();
        const lastIndex = items.length - 1;
        onIndexChange(lastIndex);
        items[lastIndex]?.focus();
        break;
    }
  },

  /**
   * Handle escape key to close modals/dropdowns
   */
  handleEscape: (e: KeyboardEvent, onEscape: () => void) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      onEscape();
    }
  }
};

/**
 * Color contrast utilities
 */
export const ColorContrast = {
  /**
   * Check if color combination meets WCAG AA standards
   */
  meetsWCAGAA: (foreground: string, background: string): boolean => {
    // This is a simplified check - in production, use a proper color contrast library
    const ratio = ColorContrast.getContrastRatio(foreground, background);
    return ratio >= 4.5; // WCAG AA standard
  },

  /**
   * Get contrast ratio between two colors (simplified)
   */
  getContrastRatio: (color1: string, color2: string): number => {
    // Simplified implementation - use a proper library like 'color' for production
    return 4.5; // Placeholder return
  }
};

/**
 * Screen reader utilities
 */
export const ScreenReader = {
  /**
   * Hide element from screen readers
   */
  hide: (element: HTMLElement) => {
    element.setAttribute('aria-hidden', 'true');
  },

  /**
   * Show element to screen readers
   */
  show: (element: HTMLElement) => {
    element.removeAttribute('aria-hidden');
  },

  /**
   * Mark element as live region
   */
  makeLiveRegion: (element: HTMLElement, priority: 'polite' | 'assertive' = 'polite') => {
    element.setAttribute('aria-live', priority);
    element.setAttribute('aria-atomic', 'true');
  }
};

/**
 * Form accessibility helpers
 */
export const FormAccessibility = {
  /**
   * Associate label with form control
   */
  associateLabel: (labelElement: HTMLElement, controlElement: HTMLElement) => {
    const id = controlElement.id || generateAccessibleId('control');
    controlElement.id = id;
    labelElement.setAttribute('for', id);
  },

  /**
   * Add error message to form control
   */
  addErrorMessage: (controlElement: HTMLElement, errorElement: HTMLElement) => {
    const errorId = errorElement.id || generateAccessibleId('error');
    errorElement.id = errorId;
    
    const describedBy = controlElement.getAttribute('aria-describedby');
    const newDescribedBy = describedBy ? `${describedBy} ${errorId}` : errorId;
    
    controlElement.setAttribute('aria-describedby', newDescribedBy);
    controlElement.setAttribute('aria-invalid', 'true');
  },

  /**
   * Remove error message from form control
   */
  removeErrorMessage: (controlElement: HTMLElement, errorId: string) => {
    const describedBy = controlElement.getAttribute('aria-describedby');
    if (describedBy) {
      const newDescribedBy = describedBy.replace(errorId, '').trim();
      if (newDescribedBy) {
        controlElement.setAttribute('aria-describedby', newDescribedBy);
      } else {
        controlElement.removeAttribute('aria-describedby');
      }
    }
    controlElement.removeAttribute('aria-invalid');
  }
};

/**
 * Motion and animation preferences
 */
export const MotionPreferences = {
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Apply animation only if user doesn't prefer reduced motion
   */
  conditionalAnimation: (element: HTMLElement, animationClass: string) => {
    if (!MotionPreferences.prefersReducedMotion()) {
      element.classList.add(animationClass);
    }
  }
};
