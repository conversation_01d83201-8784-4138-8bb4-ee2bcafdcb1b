import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { useAuth } from '@/components/auth/AuthProvider';
import { 
  Home, 
  BarChart3, 
  FileText, 
  Users, 
  Settings, 
  Menu,
  Bell,
  Search,
  Plus,
  Upload,
  MessageSquare,
  TrendingUp,
  Package,
  DollarSign,
  X
} from 'lucide-react';

interface MobileNavigationProps {
  onSearchOpen?: () => void;
  notificationCount?: number;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ 
  onSearchOpen, 
  notificationCount = 0 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { profile, signOut } = useAuth();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const franchiseeNavItems = [
    { icon: Home, label: 'Dashboard', path: '/franchisee-dashboard' },
    { icon: Upload, label: 'Sales Upload', path: '/franchisee/sales-upload' },
    { icon: BarChart3, label: 'Analytics', path: '/franchisee/analytics' },
    { icon: MessageSquare, label: 'Support', path: '/franchisee/support' },
    { icon: Settings, label: 'Settings', path: '/franchisee/settings' }
  ];

  const franchisorNavItems = [
    { icon: Home, label: 'Dashboard', path: '/franchisor-dashboard' },
    { icon: Users, label: 'Applications', path: '/franchisor/applications' },
    { icon: TrendingUp, label: 'Analytics', path: '/franchisor/analytics' },
    { icon: Package, label: 'Inventory', path: '/franchisor/inventory' },
    { icon: Settings, label: 'Settings', path: '/franchisor/settings' }
  ];

  const getNavItems = () => {
    if (profile?.role === 'franchisee') return franchiseeNavItems;
    if (profile?.role === 'franchisor') return franchisorNavItems;
    return [];
  };

  const isActivePath = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const QuickActions = () => (
    <div className="grid grid-cols-2 gap-3 p-4 bg-gray-50 rounded-lg">
      <h4 className="col-span-2 font-semibold text-gray-900 mb-2">Quick Actions</h4>
      
      {profile?.role === 'franchisee' && (
        <>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center justify-center space-x-2"
            asChild
          >
            <Link to="/franchisee/sales-upload">
              <Upload className="w-4 h-4" />
              <span>Upload Sales</span>
            </Link>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center justify-center space-x-2"
            asChild
          >
            <Link to="/franchisee/support">
              <MessageSquare className="w-4 h-4" />
              <span>Get Support</span>
            </Link>
          </Button>
        </>
      )}
      
      {profile?.role === 'franchisor' && (
        <>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center justify-center space-x-2"
            asChild
          >
            <Link to="/franchisor/applications">
              <FileText className="w-4 h-4" />
              <span>Applications</span>
            </Link>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center justify-center space-x-2"
            asChild
          >
            <Link to="/franchisor/analytics">
              <BarChart3 className="w-4 h-4" />
              <span>Analytics</span>
            </Link>
          </Button>
        </>
      )}
    </div>
  );

  return (
    <>
      {/* Mobile Top Bar */}
      <div className={`fixed top-0 left-0 right-0 z-50 bg-white border-b transition-all duration-200 ${
        isScrolled ? 'shadow-md' : ''
      } lg:hidden`}>
        <div className="flex items-center justify-between px-4 py-3">
          {/* Left: Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm">
                <Menu className="w-5 h-5" />
              </Button>
            </SheetTrigger>
            
            <SheetContent side="left" className="w-80 p-0">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="p-6 border-b bg-gradient-to-r from-blue-600 to-blue-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-xl font-bold text-white">FranchiseHub</h2>
                      <p className="text-blue-100 text-sm capitalize">
                        {profile?.role || 'User'} Portal
                      </p>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setIsOpen(false)}
                      className="text-white hover:bg-blue-500"
                    >
                      <X className="w-5 h-5" />
                    </Button>
                  </div>
                </div>

                {/* Navigation Items */}
                <div className="flex-1 overflow-y-auto py-4">
                  <nav className="space-y-1 px-4">
                    {getNavItems().map((item) => {
                      const Icon = item.icon;
                      const isActive = isActivePath(item.path);
                      
                      return (
                        <Link
                          key={item.path}
                          to={item.path}
                          onClick={() => setIsOpen(false)}
                          className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors ${
                            isActive
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <Icon className={`w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-500'}`} />
                          <span className="font-medium">{item.label}</span>
                          {isActive && (
                            <div className="ml-auto w-2 h-2 bg-blue-600 rounded-full" />
                          )}
                        </Link>
                      );
                    })}
                  </nav>

                  {/* Quick Actions */}
                  <div className="px-4 mt-6">
                    <QuickActions />
                  </div>
                </div>

                {/* Footer */}
                <div className="p-4 border-t bg-gray-50">
                  <Button 
                    variant="outline" 
                    className="w-full" 
                    onClick={() => {
                      signOut();
                      setIsOpen(false);
                    }}
                  >
                    Sign Out
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          {/* Center: Logo/Title */}
          <div className="flex-1 text-center">
            <h1 className="text-lg font-bold text-gray-900">FranchiseHub</h1>
          </div>

          {/* Right: Actions */}
          <div className="flex items-center space-x-2">
            {/* Search Button */}
            <Button 
              variant="ghost" 
              size="sm"
              onClick={onSearchOpen}
            >
              <Search className="w-5 h-5" />
            </Button>

            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-5 h-5" />
              {notificationCount > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
                  {notificationCount > 9 ? '9+' : notificationCount}
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t lg:hidden">
        <div className="grid grid-cols-5 py-2">
          {getNavItems().slice(0, 5).map((item) => {
            const Icon = item.icon;
            const isActive = isActivePath(item.path);
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex flex-col items-center justify-center py-2 px-1 transition-colors ${
                  isActive ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                <Icon className={`w-5 h-5 mb-1 ${isActive ? 'text-blue-600' : 'text-gray-500'}`} />
                <span className="text-xs font-medium truncate">{item.label}</span>
                {isActive && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full" />
                )}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Spacer for fixed navigation */}
      <div className="h-16 lg:hidden" /> {/* Top spacer */}
      <div className="h-16 lg:hidden" /> {/* Bottom spacer */}
    </>
  );
};

export default MobileNavigation;
