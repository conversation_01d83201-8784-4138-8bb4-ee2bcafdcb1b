import React, { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useRealtimeSubscription } from '@/hooks/useSupabase';
import { Wifi, WifiOff, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';

interface RealtimeSyncProps {
  tables?: string[];
  onUpdate?: (table: string, payload: any) => void;
}

const RealtimeSync: React.FC<RealtimeSyncProps> = ({ 
  tables = ['franchise_applications', 'sales_reports', 'support_tickets'], 
  onUpdate 
}) => {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);
  const [recentUpdates, setRecentUpdates] = useState<Array<{ table: string; event: string; timestamp: Date }>>([]);

  // Handle real-time updates
  const handleRealtimeUpdate = (table: string) => (payload: any) => {
    console.log(`Real-time update from ${table}:`, payload);
    
    setLastUpdate(new Date());
    setUpdateCount(prev => prev + 1);
    setConnectionStatus('connected');
    
    // Add to recent updates
    setRecentUpdates(prev => [
      { table, event: payload.eventType || 'UPDATE', timestamp: new Date() },
      ...prev.slice(0, 4) // Keep only last 5 updates
    ]);

    // Call parent callback
    if (onUpdate) {
      onUpdate(table, payload);
    }
  };

  // Set up subscriptions for each table
  useEffect(() => {
    setConnectionStatus('connecting');
    
    // Simulate connection establishment
    const timer = setTimeout(() => {
      setConnectionStatus('connected');
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Subscribe to real-time updates for each table
  tables.forEach(table => {
    useRealtimeSubscription(table, handleRealtimeUpdate(table));
  });

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'disconnected': return 'bg-red-100 text-red-800';
      case 'connecting': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return <Wifi className="w-4 h-4" />;
      case 'disconnected': return <WifiOff className="w-4 h-4" />;
      case 'connecting': return <RefreshCw className="w-4 h-4 animate-spin" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const reconnect = () => {
    setConnectionStatus('connecting');
    setTimeout(() => {
      setConnectionStatus('connected');
    }, 1000);
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
        <div className="flex items-center space-x-3">
          <Badge className={getStatusColor()}>
            {getStatusIcon()}
            <span className="ml-2 capitalize">{connectionStatus}</span>
          </Badge>
          <div className="text-sm text-gray-600">
            {connectionStatus === 'connected' && (
              <>
                <span>Real-time sync active</span>
                {lastUpdate && (
                  <span className="ml-2">
                    • Last update: {lastUpdate.toLocaleTimeString()}
                  </span>
                )}
              </>
            )}
            {connectionStatus === 'disconnected' && (
              <span>Connection lost - data may be outdated</span>
            )}
            {connectionStatus === 'connecting' && (
              <span>Establishing real-time connection...</span>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {updateCount > 0 && (
            <Badge variant="outline" className="text-xs">
              {updateCount} updates
            </Badge>
          )}
          {connectionStatus === 'disconnected' && (
            <Button size="sm" variant="outline" onClick={reconnect}>
              <RefreshCw className="w-4 h-4 mr-1" />
              Reconnect
            </Button>
          )}
        </div>
      </div>

      {/* Recent Updates */}
      {recentUpdates.length > 0 && (
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
            <CheckCircle className="w-4 h-4 mr-2" />
            Recent Updates
          </h4>
          <div className="space-y-1">
            {recentUpdates.map((update, index) => (
              <div key={index} className="text-xs text-blue-700 flex items-center justify-between">
                <span>
                  {update.table.replace('_', ' ').toUpperCase()} • {update.event}
                </span>
                <span>{update.timestamp.toLocaleTimeString()}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Connection Error */}
      {connectionStatus === 'disconnected' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-red-800">
            Real-time connection lost. Some data may not be up to date. 
            <Button 
              variant="link" 
              className="p-0 h-auto text-red-800 underline ml-1"
              onClick={reconnect}
            >
              Try reconnecting
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Subscribed Tables */}
      <div className="text-xs text-gray-500">
        Monitoring: {tables.join(', ').replace(/_/g, ' ')}
      </div>
    </div>
  );
};

export default RealtimeSync;
