import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import Navigation from '@/components/Navigation';
import SEO from '@/components/SEO';
import SupabaseService from '@/services/supabaseService';
import { useAuth } from '@/components/auth/AuthProvider';
import { useSalesReports } from '@/hooks/useSupabase';
import {
  Upload,
  Download,
  Calendar,
  DollarSign,
  TrendingUp,
  FileText,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  Loader2
} from 'lucide-react';

const SalesUpload = () => {
  const [salesData, setSalesData] = useState({
    date: new Date().toISOString().split('T')[0],
    totalSales: '',
    transactions: '',
    notes: ''
  });

  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Supabase hooks
  const { user } = useAuth();
  const { salesReports, loading: reportsLoading, createSalesReport } = useSalesReports();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      if (!user) {
        throw new Error('Please log in to submit sales reports');
      }

      // Get franchisee data
      const franchisee = await SupabaseService.getFranchiseeByProfileId(user.id);
      if (!franchisee.data) {
        throw new Error('Franchisee profile not found. Please contact support.');
      }

      // Prepare sales report data
      const reportData = {
        franchisee_id: franchisee.data.id,
        report_date: salesData.date,
        daily_sales: parseFloat(salesData.totalSales),
        transactions_count: parseInt(salesData.transactions),
        top_products: {}, // Could be enhanced to include product breakdown
        notes: salesData.notes || null
      };

      const { data, error } = await createSalesReport(reportData);

      if (error) {
        throw new Error(error);
      }

      console.log('Sales report submitted successfully:', data);
      setSubmitSuccess(true);

      // Reset form
      setSalesData({
        date: new Date().toISOString().split('T')[0],
        totalSales: '',
        transactions: '',
        notes: ''
      });
      setUploadedFile(null);

      // Hide success message after 3 seconds
      setTimeout(() => setSubmitSuccess(false), 3000);

    } catch (error: any) {
      console.error('Error submitting sales report:', error);
      setSubmitError(error.message || 'Failed to submit sales report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <SEO
        title="Sales Upload - Franchisee Dashboard"
        description="Upload daily sales reports and track your franchise performance"
        noIndex={true}
      />
      <Navigation />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button variant="ghost" asChild>
              <Link to="/franchisee-dashboard">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Sales Upload</h1>
          <p className="text-gray-600">Submit your daily sales reports and track performance</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Upload Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload className="w-5 h-5" />
                  <span>Upload Daily Sales Report</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Sales Date *</label>
                        <Input
                          type="date"
                          value={salesData.date}
                          onChange={(e) => setSalesData({...salesData, date: e.target.value})}
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Total Sales Amount *</label>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                          <Input
                            placeholder="0.00"
                            type="number"
                            step="0.01"
                            className="pl-10"
                            value={salesData.totalSales}
                            onChange={(e) => setSalesData({...salesData, totalSales: e.target.value})}
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Number of Transactions *</label>
                        <Input
                          placeholder="0"
                          type="number"
                          value={salesData.transactions}
                          onChange={(e) => setSalesData({...salesData, transactions: e.target.value})}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Upload Sales Sheet (Optional)</label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                          <input
                            type="file"
                            accept=".xlsx,.xls,.csv"
                            onChange={handleFileUpload}
                            className="hidden"
                            id="file-upload"
                          />
                          <label htmlFor="file-upload" className="cursor-pointer">
                            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600">
                              {uploadedFile ? uploadedFile.name : 'Click to upload Excel file or drag and drop'}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">Supported: .xlsx, .xls, .csv (Max 10MB)</p>
                          </label>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Additional Notes</label>
                        <Textarea
                          placeholder="Any special notes about today's sales..."
                          value={salesData.notes}
                          onChange={(e) => setSalesData({...salesData, notes: e.target.value})}
                          rows={4}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Success/Error Messages */}
                  {submitSuccess && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <span className="text-green-800 font-medium">Sales report submitted successfully!</span>
                      </div>
                    </div>
                  )}

                  {submitError && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="w-5 h-5 text-red-600" />
                        <span className="text-red-800">{submitError}</span>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end space-x-4">
                    <Button type="button" variant="outline">Save as Draft</Button>
                    <Button type="submit" disabled={isSubmitting || !salesData.totalSales || !salesData.transactions}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        'Submit Report'
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Quick Tips */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Reporting Guidelines</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                    <span>Submit reports by 11:59 PM daily for accurate tracking</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                    <span>Include all sales channels (walk-in, delivery, pickup)</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
                    <span>Upload detailed sales sheet for better analytics</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="w-4 h-4 text-blue-500 mt-0.5" />
                    <span>Contact support if you need to modify submitted reports</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Reports */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5" />
                  <span>Recent Reports</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportsLoading ? (
                    <div className="text-center py-4">
                      <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                      <span className="text-sm text-gray-600">Loading reports...</span>
                    </div>
                  ) : salesReports.length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <span className="text-sm">No sales reports yet</span>
                    </div>
                  ) : (
                    salesReports.slice(0, 4).map((report) => (
                      <div key={report.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">
                            {new Date(report.report_date).toLocaleDateString()}
                          </span>
                          <Badge className="bg-green-100 text-green-800">Submitted</Badge>
                        </div>
                        <div className="text-lg font-bold text-green-600 mb-1">
                          ₱{report.daily_sales.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-600">
                          {report.transactions_count} transactions
                        </div>
                        {report.notes && (
                          <div className="text-xs text-gray-500 mt-1 italic">
                            {report.notes}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>

                <Separator className="my-4" />

                <Button variant="outline" className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Download Report Template
                </Button>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                {reportsLoading ? (
                  <div className="text-center py-4">
                    <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Reports</span>
                      <span className="font-semibold">{salesReports.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Sales</span>
                      <span className="font-semibold">
                        ₱{salesReports.reduce((sum, report) => sum + Number(report.daily_sales), 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Daily Sales</span>
                      <span className="font-semibold">
                        ₱{salesReports.length > 0
                          ? (salesReports.reduce((sum, report) => sum + Number(report.daily_sales), 0) / salesReports.length).toLocaleString()
                          : '0'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Transactions</span>
                      <span className="font-semibold">
                        {salesReports.reduce((sum, report) => sum + report.transactions_count, 0)}
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SalesUpload;
