-- FranchiseHub Database Schema for Supabase
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('franchisor', 'franchisee', 'admin');
CREATE TYPE application_status AS ENUM ('pending', 'approved', 'rejected', 'under_review');
CREATE TYPE franchise_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE order_status AS ENUM ('pending', 'approved', 'shipped', 'delivered', 'cancelled');
CREATE TYPE ticket_category AS ENUM ('technical', 'operational', 'marketing', 'financial', 'other');
CREATE TYPE ticket_priority AS ENUM ('low', 'medium', 'high', 'urgent');
CREATE TYPE ticket_status AS ENUM ('open', 'in_progress', 'resolved', 'closed');

-- Profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role user_role DEFAULT 'franchisee',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Franchise applications table
CREATE TABLE franchise_applications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    applicant_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT NOT NULL,
    business_experience TEXT NOT NULL,
    investment_amount DECIMAL(12,2) NOT NULL,
    preferred_location TEXT NOT NULL,
    brand_interest TEXT NOT NULL,
    status application_status DEFAULT 'pending',
    documents JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_by UUID REFERENCES profiles(id),
    review_notes TEXT
);

-- Franchisees table
CREATE TABLE franchisees (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    franchise_name TEXT NOT NULL,
    brand TEXT NOT NULL,
    location TEXT NOT NULL,
    opening_date DATE NOT NULL,
    status franchise_status DEFAULT 'active',
    monthly_sales_target DECIMAL(12,2) NOT NULL,
    current_month_sales DECIMAL(12,2) DEFAULT 0,
    total_sales DECIMAL(12,2) DEFAULT 0,
    inventory_level INTEGER DEFAULT 100,
    last_inventory_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sales reports table
CREATE TABLE sales_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    franchisee_id UUID REFERENCES franchisees(id) ON DELETE CASCADE,
    report_date DATE NOT NULL,
    daily_sales DECIMAL(10,2) NOT NULL,
    transactions_count INTEGER NOT NULL,
    top_products JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(franchisee_id, report_date)
);

-- Inventory orders table
CREATE TABLE inventory_orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    franchisee_id UUID REFERENCES franchisees(id) ON DELETE CASCADE,
    order_date DATE NOT NULL,
    items JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status order_status DEFAULT 'pending',
    delivery_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support tickets table
CREATE TABLE support_tickets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    franchisee_id UUID REFERENCES franchisees(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category ticket_category NOT NULL,
    priority ticket_priority DEFAULT 'medium',
    status ticket_status DEFAULT 'open',
    assigned_to UUID REFERENCES profiles(id),
    resolution TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_franchise_applications_status ON franchise_applications(status);
CREATE INDEX idx_franchise_applications_email ON franchise_applications(email);
CREATE INDEX idx_franchisees_profile_id ON franchisees(profile_id);
CREATE INDEX idx_franchisees_brand ON franchisees(brand);
CREATE INDEX idx_franchisees_status ON franchisees(status);
CREATE INDEX idx_sales_reports_franchisee_date ON sales_reports(franchisee_id, report_date);
CREATE INDEX idx_inventory_orders_franchisee_status ON inventory_orders(franchisee_id, status);
CREATE INDEX idx_support_tickets_franchisee_status ON support_tickets(franchisee_id, status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_franchise_applications_updated_at BEFORE UPDATE ON franchise_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_franchisees_updated_at BEFORE UPDATE ON franchisees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sales_reports_updated_at BEFORE UPDATE ON sales_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_orders_updated_at BEFORE UPDATE ON inventory_orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE franchise_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE franchisees ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Franchisors can view all profiles" ON profiles FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);

-- Franchise applications policies
CREATE POLICY "Anyone can insert applications" ON franchise_applications FOR INSERT WITH CHECK (true);
CREATE POLICY "Franchisors can view all applications" ON franchise_applications FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);
CREATE POLICY "Franchisors can update applications" ON franchise_applications FOR UPDATE USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);

-- Franchisees policies
CREATE POLICY "Franchisees can view own data" ON franchisees FOR SELECT USING (
    profile_id = auth.uid()
);
CREATE POLICY "Franchisors can view all franchisees" ON franchisees FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);
CREATE POLICY "Franchisors can manage franchisees" ON franchisees FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);

-- Sales reports policies
CREATE POLICY "Franchisees can manage own sales reports" ON sales_reports FOR ALL USING (
    EXISTS (SELECT 1 FROM franchisees WHERE id = franchisee_id AND profile_id = auth.uid())
);
CREATE POLICY "Franchisors can view all sales reports" ON sales_reports FOR SELECT USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);

-- Inventory orders policies
CREATE POLICY "Franchisees can manage own inventory orders" ON inventory_orders FOR ALL USING (
    EXISTS (SELECT 1 FROM franchisees WHERE id = franchisee_id AND profile_id = auth.uid())
);
CREATE POLICY "Franchisors can view and update inventory orders" ON inventory_orders FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);

-- Support tickets policies
CREATE POLICY "Franchisees can manage own support tickets" ON support_tickets FOR ALL USING (
    EXISTS (SELECT 1 FROM franchisees WHERE id = franchisee_id AND profile_id = auth.uid())
);
CREATE POLICY "Franchisors can view and manage all support tickets" ON support_tickets FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
('documents', 'documents', false),
('avatars', 'avatars', true),
('marketing-assets', 'marketing-assets', true);

-- Storage policies
CREATE POLICY "Users can upload their own documents" ON storage.objects FOR INSERT WITH CHECK (
    bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their own documents" ON storage.objects FOR SELECT USING (
    bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Anyone can view avatars" ON storage.objects FOR SELECT USING (bucket_id = 'avatars');
CREATE POLICY "Users can upload their own avatar" ON storage.objects FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Anyone can view marketing assets" ON storage.objects FOR SELECT USING (bucket_id = 'marketing-assets');
CREATE POLICY "Franchisors can upload marketing assets" ON storage.objects FOR INSERT WITH CHECK (
    bucket_id = 'marketing-assets' AND 
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'franchisor')
);
