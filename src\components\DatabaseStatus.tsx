import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import SupabaseService from '@/services/supabaseService';
import DataMigration from '@/utils/dataMigration';
import { useAuth } from '@/components/auth/AuthProvider';
import { 
  Database, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Upload, 
  BarChart3,
  Users,
  FileText,
  Package
} from 'lucide-react';

const DatabaseStatus: React.FC = () => {
  const [status, setStatus] = useState<'checking' | 'empty' | 'populated' | 'error'>('checking');
  const [stats, setStats] = useState<any>(null);
  const [migrating, setMigrating] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState(0);
  const [migrationResults, setMigrationResults] = useState<any>(null);
  const { user } = useAuth();

  useEffect(() => {
    checkDatabaseStatus();
  }, []);

  const checkDatabaseStatus = async () => {
    try {
      setStatus('checking');
      
      // Check if we have data in key tables
      const [applications, franchisees, salesReports, tickets] = await Promise.all([
        SupabaseService.getApplications(),
        SupabaseService.getFranchisees(),
        SupabaseService.getSalesReports(),
        SupabaseService.getSupportTickets()
      ]);

      const stats = {
        applications: applications.data?.length || 0,
        franchisees: franchisees.data?.length || 0,
        salesReports: salesReports.data?.length || 0,
        supportTickets: tickets.data?.length || 0,
        total: (applications.data?.length || 0) + 
               (franchisees.data?.length || 0) + 
               (salesReports.data?.length || 0) + 
               (tickets.data?.length || 0)
      };

      setStats(stats);
      setStatus(stats.total > 0 ? 'populated' : 'empty');
    } catch (error) {
      console.error('Error checking database status:', error);
      setStatus('error');
    }
  };

  const runMigration = async () => {
    if (!user) {
      alert('Please log in to run data migration');
      return;
    }

    setMigrating(true);
    setMigrationProgress(0);
    setMigrationResults(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setMigrationProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const results = await DataMigration.runFullMigration(user.id);
      
      clearInterval(progressInterval);
      setMigrationProgress(100);
      setMigrationResults(results);
      
      // Refresh status after migration
      setTimeout(() => {
        checkDatabaseStatus();
        setMigrating(false);
        setMigrationProgress(0);
      }, 1000);
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrating(false);
      setMigrationProgress(0);
      alert('Migration failed: ' + (error as Error).message);
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'populated': return 'bg-green-100 text-green-800';
      case 'empty': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'checking': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'populated': return <CheckCircle className="w-5 h-5" />;
      case 'empty': return <Database className="w-5 h-5" />;
      case 'error': return <XCircle className="w-5 h-5" />;
      case 'checking': return <Loader2 className="w-5 h-5 animate-spin" />;
      default: return <Database className="w-5 h-5" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Database Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="w-6 h-6" />
            <span>Database Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Badge className={getStatusColor()}>
                {getStatusIcon()}
                <span className="ml-2 capitalize">{status}</span>
              </Badge>
              <div className="text-sm text-gray-600">
                {status === 'checking' && 'Checking database status...'}
                {status === 'empty' && 'Database is empty - ready for sample data'}
                {status === 'populated' && 'Database contains data'}
                {status === 'error' && 'Error connecting to database'}
              </div>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={checkDatabaseStatus}
              disabled={status === 'checking'}
            >
              {status === 'checking' ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                'Refresh'
              )}
            </Button>
          </div>

          {/* Data Statistics */}
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <FileText className="w-4 h-4 text-blue-500 mr-1" />
                  <span className="text-2xl font-bold">{stats.applications}</span>
                </div>
                <p className="text-xs text-gray-600">Applications</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Users className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-2xl font-bold">{stats.franchisees}</span>
                </div>
                <p className="text-xs text-gray-600">Franchisees</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <BarChart3 className="w-4 h-4 text-purple-500 mr-1" />
                  <span className="text-2xl font-bold">{stats.salesReports}</span>
                </div>
                <p className="text-xs text-gray-600">Sales Reports</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Package className="w-4 h-4 text-orange-500 mr-1" />
                  <span className="text-2xl font-bold">{stats.supportTickets}</span>
                </div>
                <p className="text-xs text-gray-600">Support Tickets</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Migration Section */}
      {status === 'empty' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900">Populate with Sample Data</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-blue-800">
              Your database is empty. Would you like to populate it with sample data to test the platform?
            </p>
            
            <div className="bg-white p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">Sample data includes:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 3 franchise applications with different statuses</li>
                <li>• 1 sample franchisee (linked to your account)</li>
                <li>• 3 sales reports with realistic data</li>
                <li>• 3 support tickets with various priorities</li>
              </ul>
            </div>

            {migrating ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">Migrating data...</span>
                </div>
                <Progress value={migrationProgress} className="w-full" />
                <p className="text-xs text-gray-600">{migrationProgress}% complete</p>
              </div>
            ) : (
              <Button onClick={runMigration} className="bg-blue-600 hover:bg-blue-700">
                <Upload className="w-4 h-4 mr-2" />
                Populate Sample Data
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Migration Results */}
      {migrationResults && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-green-800">
            <strong>Migration completed successfully!</strong>
            <div className="mt-2 text-sm">
              <p>Applications: {migrationResults.summary.applications.successful}/{migrationResults.summary.applications.total} migrated</p>
              <p>Sales Reports: {migrationResults.summary.salesReports.successful}/{migrationResults.summary.salesReports.total} migrated</p>
              <p>Support Tickets: {migrationResults.summary.supportTickets.successful}/{migrationResults.summary.supportTickets.total} migrated</p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Success State */}
      {status === 'populated' && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-green-800">
            <strong>Database is ready!</strong> Your FranchiseHub platform is now using real data from Supabase. 
            All dashboards, forms, and features are connected to the live database.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default DatabaseStatus;
