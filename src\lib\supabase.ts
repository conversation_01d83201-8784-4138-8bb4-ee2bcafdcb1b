import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Helper functions for common operations
export const supabaseHelpers = {
  // Authentication helpers
  auth: {
    signUp: async (email: string, password: string, metadata?: any) => {
      return await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata
        }
      });
    },

    signIn: async (email: string, password: string) => {
      return await supabase.auth.signInWithPassword({
        email,
        password
      });
    },

    signOut: async () => {
      return await supabase.auth.signOut();
    },

    getCurrentUser: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      return user;
    },

    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      return supabase.auth.onAuthStateChange(callback);
    }
  },

  // Storage helpers
  storage: {
    uploadFile: async (bucket: string, path: string, file: File) => {
      return await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false
        });
    },

    downloadFile: async (bucket: string, path: string) => {
      return await supabase.storage
        .from(bucket)
        .download(path);
    },

    getPublicUrl: (bucket: string, path: string) => {
      return supabase.storage
        .from(bucket)
        .getPublicUrl(path);
    },

    deleteFile: async (bucket: string, path: string) => {
      return await supabase.storage
        .from(bucket)
        .remove([path]);
    }
  },

  // Real-time helpers
  realtime: {
    subscribeToTable: (table: string, callback: (payload: any) => void) => {
      return supabase
        .channel(`public:${table}`)
        .on('postgres_changes', 
          { event: '*', schema: 'public', table }, 
          callback
        )
        .subscribe();
    },

    unsubscribe: (subscription: any) => {
      return supabase.removeChannel(subscription);
    }
  }
};

// Error handling helper
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  
  if (error?.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

export default supabase;
