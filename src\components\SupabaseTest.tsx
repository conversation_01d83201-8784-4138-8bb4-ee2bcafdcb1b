import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import DatabaseStatus from '@/components/DatabaseStatus';
import RealtimeSync from '@/components/RealtimeSync';
import { CheckCircle, XCircle, Database, Wifi, Shield, Activity, Settings } from 'lucide-react';

const SupabaseTest: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing');
  const [testResults, setTestResults] = useState({
    connection: false,
    auth: false,
    database: false,
    realtime: false
  });
  const [error, setError] = useState<string | null>(null);
  const [details, setDetails] = useState<any>(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    setConnectionStatus('testing');
    setError(null);

    const results = {
      connection: false,
      auth: false,
      database: false,
      realtime: false
    };

    try {
      // Test 1: Basic Connection
      console.log('Testing Supabase connection...');
      const { data: connectionTest, error: connectionError } = await supabase
        .from('_supabase_migrations')
        .select('version')
        .limit(1);

      if (!connectionError) {
        results.connection = true;
        console.log('✅ Connection successful');
      } else {
        console.log('❌ Connection failed:', connectionError.message);
      }

      // Test 2: Auth Service
      console.log('Testing Auth service...');
      try {
        const { data: authData } = await supabase.auth.getSession();
        results.auth = true;
        console.log('✅ Auth service accessible');
      } catch (authError) {
        console.log('❌ Auth service error:', authError);
      }

      // Test 3: Database Access (try to access a table)
      console.log('Testing Database access...');
      try {
        // Try to create the profiles table if it doesn't exist
        const { data: tableTest, error: tableError } = await supabase
          .from('profiles')
          .select('id')
          .limit(1);

        if (!tableError || tableError.code === 'PGRST116') {
          results.database = true;
          console.log('✅ Database accessible');
        } else {
          console.log('❌ Database error:', tableError.message);
        }
      } catch (dbError) {
        console.log('❌ Database access error:', dbError);
      }

      // Test 4: Realtime
      console.log('Testing Realtime...');
      try {
        const channel = supabase.channel('test-channel');
        if (channel) {
          results.realtime = true;
          console.log('✅ Realtime accessible');
          supabase.removeChannel(channel);
        }
      } catch (realtimeError) {
        console.log('❌ Realtime error:', realtimeError);
      }

      setTestResults(results);

      if (results.connection && results.auth) {
        setConnectionStatus('connected');
        setDetails({
          url: import.meta.env.VITE_SUPABASE_URL,
          project: import.meta.env.VITE_SUPABASE_URL?.split('//')[1]?.split('.')[0],
          timestamp: new Date().toISOString()
        });
      } else {
        setConnectionStatus('error');
        setError('Failed to establish connection to Supabase');
      }

    } catch (err: any) {
      console.error('Connection test failed:', err);
      setConnectionStatus('error');
      setError(err.message || 'Unknown error occurred');
    }
  };

  const setupDatabase = async () => {
    try {
      console.log('Setting up database schema...');

      // Create profiles table
      const { error: profilesError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS profiles (
            id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT,
            avatar_url TEXT,
            role TEXT DEFAULT 'franchisee',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });

      if (profilesError) {
        console.error('Error creating profiles table:', profilesError);
        setError('Failed to create database schema. Please run the SQL scripts manually in Supabase dashboard.');
        return;
      }

      console.log('✅ Database schema created successfully');
      testConnection(); // Re-test after setup

    } catch (err: any) {
      console.error('Database setup failed:', err);
      setError('Database setup failed. Please run the SQL scripts manually in Supabase dashboard.');
    }
  };

  const StatusIcon = ({ status }: { status: boolean }) => (
    status ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    )
  );

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="w-6 h-6" />
            <span>Supabase Integration Center</span>
            <Badge variant={connectionStatus === 'connected' ? 'default' : connectionStatus === 'error' ? 'destructive' : 'secondary'}>
              {connectionStatus === 'testing' ? 'Testing...' : connectionStatus === 'connected' ? 'Connected' : 'Error'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="connection" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="connection" className="flex items-center space-x-2">
                <Wifi className="w-4 h-4" />
                <span>Connection</span>
              </TabsTrigger>
              <TabsTrigger value="database" className="flex items-center space-x-2">
                <Database className="w-4 h-4" />
                <span>Database</span>
              </TabsTrigger>
              <TabsTrigger value="realtime" className="flex items-center space-x-2">
                <Activity className="w-4 h-4" />
                <span>Real-time</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="connection" className="space-y-4 mt-6">
        {/* Connection Status */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <StatusIcon status={testResults.connection} />
            <span>Database Connection</span>
          </div>
          <div className="flex items-center space-x-2">
            <StatusIcon status={testResults.auth} />
            <span>Authentication Service</span>
          </div>
          <div className="flex items-center space-x-2">
            <StatusIcon status={testResults.database} />
            <span>Database Tables</span>
          </div>
          <div className="flex items-center space-x-2">
            <StatusIcon status={testResults.realtime} />
            <span>Realtime Features</span>
          </div>
        </div>

        {/* Connection Details */}
        {details && (
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Connection Details</h4>
            <div className="text-sm text-green-700 space-y-1">
              <p><strong>Project:</strong> {details.project}</p>
              <p><strong>URL:</strong> {details.url}</p>
              <p><strong>Connected at:</strong> {new Date(details.timestamp).toLocaleString()}</p>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <XCircle className="h-4 w-4" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex space-x-2">
          <Button onClick={testConnection} variant="outline">
            Test Connection
          </Button>

          {connectionStatus === 'connected' && !testResults.database && (
            <Button onClick={setupDatabase} className="bg-blue-600 hover:bg-blue-700">
              Setup Database
            </Button>
          )}
        </div>

        {/* Setup Instructions */}
        {connectionStatus === 'connected' && !testResults.database && (
          <Alert className="border-blue-200 bg-blue-50">
            <Shield className="h-4 w-4" />
            <AlertDescription className="text-blue-800">
              <strong>Next Step:</strong> Your Supabase connection is working! Now you need to set up the database schema.
              Go to your Supabase dashboard → SQL Editor and run the scripts from <code>database/schema.sql</code> and <code>database/sample-data.sql</code>.
            </AlertDescription>
          </Alert>
        )}

        {/* Success Message */}
        {connectionStatus === 'connected' && testResults.database && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-800">
              <strong>🎉 Success!</strong> Your Supabase integration is fully working! You can now use real-time data, authentication, and all database features.
            </AlertDescription>
          </Alert>
        )}

        {/* Manual Setup Instructions */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Manual Setup Instructions</h4>
                <div className="text-sm text-gray-700 space-y-2">
                  <p>1. Go to your <a href="https://qiqkdgsjilpndpcfedni.supabase.co" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Supabase Dashboard</a></p>
                  <p>2. Navigate to <strong>SQL Editor</strong></p>
                  <p>3. Copy and run the contents of <code>database/schema.sql</code></p>
                  <p>4. Copy and run the contents of <code>database/sample-data.sql</code> (optional demo data)</p>
                  <p>5. Click "Test Connection" above to verify everything is working</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="database" className="space-y-4 mt-6">
              <DatabaseStatus />
            </TabsContent>

            <TabsContent value="realtime" className="space-y-4 mt-6">
              <RealtimeSync />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default SupabaseTest;
